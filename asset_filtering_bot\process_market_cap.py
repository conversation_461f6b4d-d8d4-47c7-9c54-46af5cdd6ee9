#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import polars as pl
import glob
from datetime import datetime
import argparse

def process_market_cap_data(input_dir="coins_mkc_history", output_path="topk_market_cap.parquet", top_k=100, keep_incomplete=True):
    """
    处理加密货币的历史市值数据，得到每一天的topK市值的资产数据

    参数:
    input_dir (str): 输入目录，包含所有加密货币的历史市值数据
    output_path (str): 输出文件路径
    top_k (int): 每个时间戳保留的市值最高的资产数量
    keep_incomplete (bool): 是否保留无法凑齐topK个资产的时间点

    返回:
    None
    """
    print(f"开始处理数据，参数: top_k={top_k}, keep_incomplete={keep_incomplete}")

    # 读取collected_coins_interval.parquet文件
    coins_info = pl.read_parquet(os.path.join(input_dir, "collected_coins_interval.parquet"))
    print(f"读取到 {len(coins_info)} 个加密货币的信息")

    # 获取所有parquet文件路径（排除collected_coins_interval.parquet）
    parquet_files = [f for f in glob.glob(os.path.join(input_dir, "*.parquet"))
                     if os.path.basename(f) != "collected_coins_interval.parquet"]
    print(f"找到 {len(parquet_files)} 个加密货币历史市值数据文件")

    # 创建一个空的DataFrame列表，用于存储所有数据
    all_data = []

    # 读取所有parquet文件并添加symbol列
    for file_path in parquet_files:
        try:
            # 从文件名中提取symbol
            file_name = os.path.basename(file_path)
            symbol = file_name.split('_')[0]

            # 读取parquet文件
            df = pl.read_parquet(file_path)

            # 确保数据类型一致
            df = df.with_columns([
                pl.col("price").cast(pl.Float64),
                pl.col("vol_spot_24h").cast(pl.Float64),
                pl.col("market_cap").cast(pl.Float64),
                pl.col("timestamp").cast(pl.Int64)
            ])

            # 添加symbol列
            df = df.with_columns(pl.lit(symbol).alias("symbol"))

            # 添加到列表中
            all_data.append(df)
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")

    # 合并所有数据
    print("合并所有数据...")
    combined_data = pl.concat(all_data)

    # 将timestamp转换为日期格式，便于后续处理
    combined_data = combined_data.with_columns(
        pl.col("timestamp").map_elements(
            lambda ts: datetime.fromtimestamp(ts / 1000).date().isoformat(),
            return_dtype=pl.Utf8
        ).alias("date")
    )

    # 按日期分组，并找出每个日期市值最高的top_k个资产
    print(f"计算每个日期市值最高的 {top_k} 个资产...")

    # 按日期和symbol分组，取每个symbol在每个日期的最新数据
    latest_data = combined_data.group_by(["date", "symbol"]).agg(
        pl.col("market_cap").last().alias("market_cap"),
        pl.col("vol_spot_24h").last().alias("vol_spot_24h"),
        pl.col("timestamp").last().alias("timestamp")
    )

    # 按日期分组，并按市值降序排序
    grouped_data = latest_data.sort(["date", "market_cap"], descending=[False, True])
    grouped_data = grouped_data.filter(pl.col("market_cap").is_not_null())

    # 计算每个日期的资产数量
    date_counts = grouped_data.group_by("date").agg(pl.len().alias("count"))

    # 如果不保留不完整的日期，则过滤掉资产数量少于top_k的日期
    if not keep_incomplete:
        valid_dates = date_counts.filter(pl.col("count") >= top_k)["date"]
        grouped_data = grouped_data.filter(pl.col("date").is_in(valid_dates))

    # 对每个日期取市值最高的top_k个资产
    # 首先按日期和市值排序，然后按24小时交易量排序
    sorted_data = grouped_data.sort(["date", "market_cap", "vol_spot_24h"], descending=[False, True, True])

    # 然后为每个日期选择前top_k个资产
    result_data = []
    for date in sorted_data["date"].unique():
        date_data = sorted_data.filter(pl.col("date") == date)
        # 过滤掉市值为null的数据
        # date_data = date_data.filter(pl.col("market_cap").is_not_null())
        top_assets = date_data.head(top_k)
        assets_list = [{"asset": s, "market_cap": m, "vol_spot_24h": v} 
                      for s, m, v in zip(top_assets["symbol"], 
                                        top_assets["market_cap"],
                                        top_assets["vol_spot_24h"])]
        result_data.append({"date": date, "topK": assets_list})

    # 创建结果DataFrame
    result = pl.DataFrame(result_data)

    # 过滤掉没有有效市值数据的日期
    result = result.filter(pl.col("topK").list.len() > 0)

    # 按日期排序
    result = result.sort("date")

    # 保存结果
    print(f"保存结果到 {output_path}...")
    result.write_parquet(output_path)

    print(f"处理完成，结果已保存到 {output_path}")
    print(f"结果包含 {len(result)} 个日期的数据")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="处理加密货币的历史市值数据")
    parser.add_argument("--input_dir", type=str, default="coins_mkc_history", help="输入目录，包含所有加密货币的历史市值数据")
    parser.add_argument("--output_path", type=str, default="topk_market_cap.parquet", help="输出文件路径")
    parser.add_argument("--top_k", type=int, default=100, help="每个时间戳保留的市值最高的资产数量")
    parser.add_argument("--keep_incomplete", action="store_true", default=False, help="是否保留无法凑齐topK个资产的时间点")

    args = parser.parse_args()

    process_market_cap_data(
        input_dir=args.input_dir,
        output_path=args.output_path,
        top_k=args.top_k,
        keep_incomplete=args.keep_incomplete
    )



