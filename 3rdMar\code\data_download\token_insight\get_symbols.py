import os
import sys
import glob
import pandas as pd
sys.path.append("/data")

# 配置路径参数
directory = "/data/cctx_okx/spot/30m"  # 目标目录路径
output_file = "symbol_list.csv"       # 输出CSV文件名

# 获取所有.parquet文件路径
parquet_files = glob.glob(os.path.join(directory, "*_USDT.parquet"))

# 提取symbol名称（移除_USDT.parquet后缀）
symbols = [
    os.path.basename(f).replace("_USDT.parquet", "") 
    for f in parquet_files
]

# 创建DataFrame并保存为CSV
df = pd.DataFrame({"symbol": symbols})
df.to_csv(output_file, index=False)

print(f"成功生成 {len(symbols)} 个symbol，已保存至: {output_file}")