import numpy as np
import pandas as pd
import polars as pl
import matplotlib.pyplot as plt
from scipy.special import softmax
from typing import List, Tuple, Union, Optional
import datetime as dt

class TimeSeriesDataProcessor:
    """
    时序数据处理器，负责数据加载、预处理和特征工程
    """
    
    def __init__(self, window_size: int = 10):
        """
        初始化数据处理器
        
        参数:
            window_size: 时间窗口大小，用于特征提取
        """
        self.window_size = window_size
        self.data = None
        self.processed_data = None
        self.X_train = None
        self.y_train = None
        self.X_test = None
        self.y_test = None
        self.feature_columns = ['open', 'high', 'low', 'close', 'volume']
        
    def load_data(self, file_path: str) -> None:
        """
        加载数据
        
        参数:
            file_path: 数据文件路径
        """
        try:
            # 尝试使用polars加载
            self.data = pl.read_csv(file_path)
            # 转换为pandas以便兼容性
            self.data = self.data.to_pandas()
        except:
            # 如果失败，使用pandas加载
            self.data = pd.read_csv(file_path)
        
        # 确保时间列是datetime类型
        if 'candle_begin_time_GMT8' in self.data.columns:
            self.data['candle_begin_time_GMT8'] = pd.to_datetime(self.data['candle_begin_time_GMT8'])
            self.data.set_index('candle_begin_time_GMT8', inplace=True)
        
        print(f"数据加载完成，共 {len(self.data)} 条记录")
        
    def preprocess_data(self) -> None:
        """
        数据预处理，包括缺失值处理、异常值处理等
        """
        if self.data is None:
            raise ValueError("请先加载数据")
        
        # 处理缺失值
        self.processed_data = self.data.copy()
        self.processed_data = self.processed_data.fillna(method='ffill')
        
        # 计算价格变化
        self.processed_data['price_change'] = self.processed_data['close'].pct_change()
        
        # 定义价格趋势标签（1表示上涨，0表示下跌）
        self.processed_data['trend'] = (self.processed_data['close'].shift(-1) > self.processed_data['close']).astype(int)
        
        # 删除含有NaN的行
        self.processed_data = self.processed_data.dropna()
        
        print(f"数据预处理完成，处理后共 {len(self.processed_data)} 条记录")
        
    def create_features(self) -> None:
        """
        创建特征，使用时间窗口生成序列特征
        """
        if self.processed_data is None:
            raise ValueError("请先进行数据预处理")
        
        # 标准化特征
        for col in self.feature_columns:
            mean = self.processed_data[col].mean()
            std = self.processed_data[col].std()
            self.processed_data[f'{col}_norm'] = (self.processed_data[col] - mean) / std
        
        norm_columns = [f'{col}_norm' for col in self.feature_columns]
        
        # 创建窗口特征
        X = []
        y = []
        
        for i in range(self.window_size, len(self.processed_data)):
            X.append(self.processed_data[norm_columns].iloc[i-self.window_size:i].values)
            y.append(self.processed_data['trend'].iloc[i-1])
        
        self.X = np.array(X)
        self.y = np.array(y)
        
        print(f"特征创建完成，X形状: {self.X.shape}, y形状: {self.y.shape}")
        
    def split_train_test(self, test_size: float = 0.2) -> None:
        """
        分割训练集和测试集
        
        参数:
            test_size: 测试集占比
        """
        if self.X is None or self.y is None:
            raise ValueError("请先创建特征")
        
        # 计算分割点
        split_idx = int(len(self.X) * (1 - test_size))
        
        self.X_train = self.X[:split_idx]
        self.y_train = self.y[:split_idx]
        self.X_test = self.X[split_idx:]
        self.y_test = self.y[split_idx:]
        
        print(f"数据集分割完成，训练集: {len(self.X_train)} 样本，测试集: {len(self.X_test)} 样本")


class SimpleRNN:
    """
    简单RNN模型，使用numpy实现
    """
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int):
        """
        初始化RNN模型
        
        参数:
            input_dim: 输入维度
            hidden_dim: 隐藏层维度
            output_dim: 输出维度
        """
        # 初始化权重
        self.Wxh = np.random.randn(hidden_dim, input_dim) * 0.01  # 输入到隐藏层权重
        self.Whh = np.random.randn(hidden_dim, hidden_dim) * 0.01  # 隐藏层到隐藏层权重
        self.Why = np.random.randn(output_dim, hidden_dim) * 0.01  # 隐藏层到输出层权重
        
        # 初始化偏置
        self.bh = np.zeros((hidden_dim, 1))  # 隐藏层偏置
        self.by = np.zeros((output_dim, 1))  # 输出层偏置
        
        # 模型参数
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        
    def forward(self, inputs: np.ndarray) -> Tuple[np.ndarray, List[np.ndarray], List[np.ndarray]]:
        """
        前向传播
        
        参数:
            inputs: 输入序列，形状为 (seq_len, input_dim)
            
        返回:
            输出，隐藏状态列表，输出列表
        """
        h = np.zeros((self.hidden_dim, 1))  # 初始隐藏状态
        hs = []  # 存储所有隐藏状态
        ys = []  # 存储所有输出
        
        # 遍历序列中的每个时间步
        for t in range(len(inputs)):
            x = inputs[t].reshape(-1, 1)  # 当前时间步的输入
            h = np.tanh(np.dot(self.Wxh, x) + np.dot(self.Whh, h) + self.bh)  # 计算隐藏状态
            y = np.dot(self.Why, h) + self.by  # 计算输出
            hs.append(h)
            ys.append(y)
        
        # 对最后一个输出应用softmax得到概率分布
        probs = softmax(ys[-1].flatten())
        
        return probs, hs, ys
    
    def backward(self, inputs: np.ndarray, targets: np.ndarray, hs: List[np.ndarray], 
                 ys: List[np.ndarray]) -> Tuple[dict, float]:
        """
        反向传播
        
        参数:
            inputs: 输入序列
            targets: 目标值
            hs: 隐藏状态列表
            ys: 输出列表
            
        返回:
            梯度字典，损失值
        """
        # 初始化梯度
        dWxh = np.zeros_like(self.Wxh)
        dWhh = np.zeros_like(self.Whh)
        dWhy = np.zeros_like(self.Why)
        dbh = np.zeros_like(self.bh)
        dby = np.zeros_like(self.by)
        
        # 初始化上一时间步的隐藏状态梯度
        dhnext = np.zeros_like(hs[0])
        
        # 计算损失
        last_y = ys[-1]
        probs = softmax(last_y.flatten())
        loss = -np.log(probs[targets])  # 交叉熵损失
        
        # 计算输出层梯度
        dy = probs.copy()
        dy[targets] -= 1  # 交叉熵损失对logits的梯度
        dy = dy.reshape(-1, 1)
        
        # 反向传播
        dWhy += np.dot(dy, hs[-1].T)
        dby += dy
        
        # 通过时间反向传播
        for t in reversed(range(len(inputs))):
            # 当前时间步的隐藏状态梯度
            dh = np.dot(self.Why.T, dy) + dhnext
            
            # tanh导数: (1 - tanh^2)
            dhraw = (1 - hs[t] * hs[t]) * dh
            
            # 更新梯度
            dbh += dhraw
            dWxh += np.dot(dhraw, inputs[t].reshape(1, -1))
            
            # 计算t-1时刻的隐藏状态梯度
            if t > 0:
                dWhh += np.dot(dhraw, hs[t-1].T)
                dhnext = np.dot(self.Whh.T, dhraw)
            else:
                dWhh += np.dot(dhraw, np.zeros((self.hidden_dim, 1)).T)
        
        # 裁剪梯度，防止梯度爆炸
        for grad in [dWxh, dWhh, dWhy, dbh, dby]:
            np.clip(grad, -5, 5, out=grad)
        
        gradients = {
            'Wxh': dWxh, 'Whh': dWhh, 'Why': dWhy,
            'bh': dbh, 'by': dby
        }
        
        return gradients, loss
    
    def update_params(self, gradients: dict, learning_rate: float = 0.01) -> None:
        """
        更新模型参数
        
        参数:
            gradients: 梯度字典
            learning_rate: 学习率
        """
        self.Wxh -= learning_rate * gradients['Wxh']
        self.Whh -= learning_rate * gradients['Whh']
        self.Why -= learning_rate * gradients['Why']
        self.bh -= learning_rate * gradients['bh']
        self.by -= learning_rate * gradients['by']


class NGramModel:
    """
    N-Gram模型用于时序预测
    """
    
    def __init__(self, n: int = 3):
        """
        初始化N-Gram模型
        
        参数:
            n: n-gram中的n
        """
        self.n = n
        self.pattern_counts = {}  # 存储模式及其后续趋势的计数
        self.patterns = {}  # 存储模式到预测趋势的映射
        
    def discretize_price_changes(self, changes: np.ndarray, n_bins: int = 3) -> List[int]:
        """
        将价格变化离散化为有限的符号
        
        参数:
            changes: 价格变化序列
            n_bins: 分箱数量
            
        返回:
            离散化后的序列
        """
        bins = np.linspace(np.min(changes), np.max(changes), n_bins + 1)
        discretized = np.digitize(changes, bins[1:-1])
        return discretized.tolist()
    
    def train(self, price_changes: np.ndarray, trends: np.ndarray) -> None:
        """
        训练N-Gram模型
        
        参数:
            price_changes: 价格变化序列
            trends: 趋势标签序列
        """
        # 离散化价格变化
        discrete_changes = self.discretize_price_changes(price_changes)
        
        # 统计n-gram模式
        for i in range(len(discrete_changes) - self.n):
            pattern = tuple(discrete_changes[i:i+self.n])
            next_trend = trends[i+self.n]
            
            if pattern not in self.pattern_counts:
                self.pattern_counts[pattern] = [0, 0]  # [下跌计数, 上涨计数]
            
            self.pattern_counts[pattern][next_trend] += 1
        
        # 计算每个模式的预测趋势
        for pattern, counts in self.pattern_counts.items():
            total = sum(counts)
            if total > 0:
                # 如果上涨计数更多，预测上涨(1)，否则预测下跌(0)
                prediction = 1 if counts[1] > counts[0] else 0
                self.patterns[pattern] = prediction
    
    def predict(self, sequence: List[int]) -> int:
        """
        预测序列的下一个趋势
        
        参数:
            sequence: 输入序列
            
        返回:
            预测的趋势(0或1)
        """
        pattern = tuple(sequence[-self.n:])
        
        if pattern in self.patterns:
            return self.patterns[pattern]
        else:
            # 如果模式未见过，返回默认预测
            return 1  # 默认预测上涨


class TimeSeriesModelTrainer:
    """
    时序模型训练器
    """
    
    def __init__(self, data_processor: TimeSeriesDataProcessor, model_type: str = 'rnn'):
        """
        初始化训练器
        
        参数:
            data_processor: 数据处理器实例
            model_type: 模型类型，'rnn'或'ngram'
        """
        self.data_processor = data_processor
        self.model_type = model_type
        self.model = None
        self.train_losses = []
        self.val_accuracies = []
        
    def create_model(self) -> None:
        """
        创建模型实例
        """
        if self.model_type == 'rnn':
            # 获取输入维度（特征数量）
            feature_dim = self.data_processor.X_train.shape[2]
            self.model = SimpleRNN(input_dim=feature_dim, hidden_dim=64, output_dim=2)
        elif self.model_type == 'ngram':
            self.model = NGramModel(n=3)
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
        
        print(f"创建{self.model_type}模型成功")
    
    def train_rnn(self, epochs: int = 50, learning_rate: float = 0.01) -> None:
        """
        训练RNN模型
        
        参数:
            epochs: 训练轮数
            learning_rate: 学习率
        """
        X_train = self.data_processor.X_train
        y_train = self.data_processor.y_train
        X_val = self.data_processor.X_test
        y_val = self.data_processor.y_test
        
        print(f"开始训练RNN模型，共{epochs}轮")
        
        for epoch in range(epochs):
            total_loss = 0
            
            # 随机打乱训练数据
            indices = np.random.permutation(len(X_train))
            X_shuffled = X_train[indices]
            y_shuffled = y_train[indices]
            
            # 批量训练
            batch_size = 32
            n_batches = len(X_shuffled) // batch_size
            
            for i in range(n_batches):
                batch_start = i * batch_size
                batch_end = (i + 1) * batch_size
                
                X_batch = X_shuffled[batch_start:batch_end]
                y_batch = y_shuffled[batch_start:batch_end]
                
                batch_loss = 0
                
                # 对批次中的每个样本进行训练
                for j in range(len(X_batch)):
                    x = X_batch[j]
                    y = y_batch[j]
                    
                    # 前向传播
                    probs, hs, ys = self.model.forward(x)
                    
                    # 反向传播
                    gradients, loss = self.model.backward(x, y, hs, ys)
                    
                    # 更新参数
                    self.model.update_params(gradients, learning_rate)
                    
                    batch_loss += loss
                
                # 计算平均批次损失
                batch_loss /= len(X_batch)
                total_loss += batch_loss
            
            # 计算平均训练损失
            avg_loss = total_loss / n_batches
            self.train_losses.append(avg_loss)
            
            # 在验证集上评估
            val_acc = self.evaluate_rnn(X_val, y_val)
            self.val_accuracies.append(val_acc)
            
            if epoch % 5 == 0:
                print(f"轮次 {epoch}/{epochs}, 训练损失: {avg_loss:.4f}, 验证准确率: {val_acc:.4f}")
        
        print("RNN模型训练完成")
    
    def train_ngram(self) -> None:
        """
        训练N-Gram模型
        """
        # 提取价格变化
        price_changes = self.data_processor.processed_data['price_change'].values
        trends = self.data_processor.processed_data['trend'].values
        
        # 分割训练集
        train_size = int(len(price_changes) * 0.8)
        price_changes_train = price_changes[:train_size]
        trends_train = trends[:train_size]
        
        print("开始训练N-Gram模型")
        self.model.train(price_changes_train, trends_train)
        print("N-Gram模型训练完成")
    
    def train(self, **kwargs) -> None:
        """
        训练模型
        
        参数:
            **kwargs: 传递给具体训练方法的参数
        """
        if self.model is None:
            self.create_model()
        
        if self.model_type == 'rnn':
            self.train_rnn(**kwargs)
        elif self.model_type == 'ngram':
            self.train_ngram()
    
    def evaluate_rnn(self, X: np.ndarray, y: np.ndarray) -> float:
        """
        评估RNN模型
        
        参数:
            X: 特征数据
            y: 标签数据
            
        返回:
            准确率
        """
        correct = 0
        
        for i in range(len(X)):
            probs, _, _ = self.model.forward(X[i])
            prediction = np.argmax(probs)
            if prediction == y[i]:
                correct += 1
        
        accuracy = correct / len(X)
        return accuracy
    
    def evaluate_ngram(self, X: np.ndarray, y: np.ndarray) -> float:
        """
        评估N-Gram模型
        
        参数:
            X: 特征数据
            y: 标签数据
            
        返回:
            准确率
        """
        # 提取价格变化
        price_changes = self.data_processor.processed_data['price_change'].values
        trends = self.data_processor.processed_data['trend'].values
        
        # 分割测试集
        train_size = int(len(price_changes) * 0.8)
        price_changes_test = price_changes[train_size:]
        trends_test = trends[train_size:]
        
        discrete_changes = self.model.discretize_price_changes(price_changes_test)
        
        correct = 0
        total = 0
        
        for i in range(self.model.n, len(discrete_changes)):
            sequence = discrete_changes[i-self.model.n:i]
            prediction = self.model.predict(sequence)
            
            if i < len(trends_test) and prediction == trends_test[i]:
                correct += 1
            total += 1
        
        accuracy = correct / total if total > 0 else 0
        return accuracy
    
    def evaluate(self) -> float:
        """
        评估模型
        
        返回:
            准确率
        """
        if self.model_type == 'rnn':
            return self.evaluate_rnn(self.data_processor.X_test, self.data_processor.y_test)
        elif self.model_type == 'ngram':
            return self.evaluate_ngram(self.data_processor.X_test, self.data_processor.y_test)
    
    def predict(self, x: np.ndarray) -> int:
        """
        预测单个样本
        
        参数:
            x: 输入特征
            
        返回:
            预测结果(0或1)
        """
        if self.model_type == 'rnn':
            probs, _, _ = self.model.forward(x)
            return np.argmax(probs)
        elif self.model_type == 'ngram':
            # 对于N-Gram，需要先将输入转换为离散符号
            if isinstance(x, np.ndarray) and x.ndim > 1:
                # 从多维特征中提取价格变化
                price_changes = x[:, -1, 0]  # 假设最后一个特征是价格
                discrete_changes = self.model.discretize_price_changes(price_changes)
                return self.model.predict(discrete_changes)
            else:
                return 1  # 默认预测
    
    def visualize_results(self) -> None:
        """
        可视化训练结果
        """
        plt.figure(figsize=(12, 5))
        
        if self.model_type == 'rnn':
            # 绘制训练损失
            plt.subplot(1, 2, 1)
            plt.plot(self.train_losses)
            plt.title('训练损失')
            plt.xlabel('轮次')
            plt.ylabel('损失')
            
            # 绘制验证准确率
            plt.subplot(1, 2, 2)
            plt.plot(self.val_accuracies)
            plt.title('验证准确率')
            plt.xlabel('轮次')
            plt.ylabel('准确率')
        
        elif self.model_type == 'ngram':
            # 获取测试集价格
            prices = self.data_processor.processed_data['close'].values
            train_size = int(len(prices) * 0.8)
            test_prices = prices[train_size:]
            
            # 获取测试集预测
            predictions = []
            
            # 提取价格变化
            price_changes = self.data_processor.processed_data['price_change'].values
            discrete_changes = self.model.discretize_price_changes(price_changes)
            
            for i in range(train_size + self.model.n, len(discrete_changes)):
                sequence = discrete_changes[i-self.model.n:i]
                prediction = self.model.predict(sequence)
                predictions.append(prediction)
            
            # 绘制价格和预测趋势
            plt.plot(test_prices, label='实际价格')
            
            # 标记预测上涨的点
            up_indices = [i for i, p in enumerate(predictions) if p == 1]
            plt.scatter([i + train_size + self.model.n for i in up_indices], 
                       [test_prices[i] for i in up_indices], 
                       color='green', marker='^', label='预测上涨')
            
            # 标记预测下跌的点
            down_indices = [i for i, p in enumerate(predictions) if p == 0]
            plt.scatter([i + train_size + self.model.n for i in down_indices], 
                       [test_prices[i] for i in down_indices], 
                       color='red', marker='v', label='预测下跌')
            
            plt.title('N-Gram模型预测结果')
            plt.xlabel('时间')
            plt.ylabel('价格')
            plt.legend()
        
        plt.tight_layout()
        plt.show()


def main():
    # 设置随机种子，保证结果可复现
    np.random.seed(42)
    
    # 创建数据处理器
    data_processor = TimeSeriesDataProcessor(window_size=10)
    
    # 演示：假设我们已经有CSV数据文件
    print("模拟加载和处理数据...")
    
    # 创建模拟数据
    dates = pd.date_range(start='2019-01-01 08:00:00', periods=1000, freq='30min')
    data = pd.DataFrame({
        'candle_begin_time_GMT8': dates,
        'open': np.random.normal(3700, 50, 1000),
        'high': np.random.normal(3750, 50, 1000),
        'low': np.random.normal(3650, 50, 1000),
        'close': np.random.normal(3700, 50, 1000),
        'volume': np.random.normal(200, 50, 1000)
    })
    
    # 模拟价格趋势
    for i in range(1, len(data)):
        data.loc[i, 'high'] = max(data.loc[i, 'open'], data.loc[i, 'close']) + np.random.normal(10, 5)
        data.loc[i, 'low'] = min(data.loc[i, 'open'], data.loc[i, 'close']) - np.random.normal(10, 5)
    
    # 保存模拟数据到CSV文件
    data.to_csv('sample_price_data.csv', index=False)
    
    # 加载数据
    data_processor.data = data
    data_processor.data.set_index('candle_begin_time_GMT8', inplace=True)
    
    # 预处理数据
    data_processor.preprocess_data()
    
    # 创建特征
    data_processor.create_features()
    
    # 分割训练集和测试集
    data_processor.split_train_test(test_size=0.2)
    
    # 创建和训练RNN模型
    print("\n===== 训练RNN模型 =====")
    rnn_trainer = TimeSeriesModelTrainer(data_processor, model_type='rnn')
    rnn_trainer.train(epochs=50, learning_rate=0.01)
    
    # 评估RNN模型
    rnn_accuracy = rnn_trainer.evaluate()
    print(f"RNN模型在测试集上的准确率: {rnn_accuracy:.4f}")
    
    # 可视化RNN模型结果
    rnn_trainer.visualize_results()
    
    # 创建和训练N-Gram模型
    print("\n===== 训练N-Gram模型 =====")
    ngram_trainer = TimeSeriesModelTrainer(data_processor, model_type='ngram')
    ngram_trainer.train()
    
    # 评估N-Gram模型
    ngram_accuracy = ngram_trainer.evaluate()
    print(f"N-Gram模型在测试集上的准确率: {ngram_accuracy:.4f}")
    
    # 可视化N-Gram模型结果
    ngram_trainer.visualize_results()
    
    print("\n模型比较:")
    print(f"RNN模型准确率: {rnn_accuracy:.4f}")
    print(f"N-Gram模型准确率: {ngram_accuracy:.4f}")


if __name__ == "__main__":
    main()
