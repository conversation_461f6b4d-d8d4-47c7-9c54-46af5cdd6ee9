# 导入函数库
from jqdata import *
from datetime import datetime, timedelta
import pandas as pd
import math
from sortedcontainers import SortedDict
import numpy as np
import talib

class Node:
    """节点类，用于存储日期和对应的值"""
    def __init__(self, date, val):
        self.date = date
        self.val = val

    def __repr__(self):
        return f"Node(date={self.date}, val={self.val})"

class Zset:
    """有序集合类，用于维护历史数据和计算分位值"""
    def __init__(self, series, percentile1=0.3, percentile2=0.7):
        self.nodes = SortedDict()
        self.val_nodes_length = SortedDict()
        self.total_nodes = 0
        self.date_index = {}
        self.percentile1 = percentile1 
        self.percentile2 = percentile2
        
        self.percent_index1 = None 
        self.percent_index2 = None
        
        for k, v in series.items():
            node = Node(k, v)
            self.insert(node)

    def _update_val_nodes_length(self, key, delta):
        if key not in self.val_nodes_length:
            self.val_nodes_length[key] = 0
        
        self.val_nodes_length[key] += delta
        self.total_nodes += delta

    def insert(self, node):
        # 插入节点到SortedDict中，保持有序
        if node.val not in self.nodes:
            self.nodes[node.val] = []
        self.nodes[node.val].append(node)
        # 同时更新date_index以实现O(1)查询
        self.date_index[node.date] = node
        
        self._update_val_nodes_length(node.val, 1)

    def delete(self, date):
        # 通过date查询并删除节点
        if date in self.date_index:
            node = self.date_index[date]
            # 从SortedDict中删除节点
            self.nodes[node.val].remove(node)
            self._update_val_nodes_length(node.val, -1)
            if not self.nodes[node.val]:  # 如果该val下没有其他节点，删除该val键
                del self.nodes[node.val]
                del self.val_nodes_length[node.val]
            del self.date_index[date]
    
    def get_percentiles(self):
        """获取当前数据的分位值"""
        self.percent_index1 = int(self.percentile1 * self.total_nodes)
        self.percent_index2 = int((1 - self.percentile2) * self.total_nodes) # 用于反向遍历
        
        percentile1_val = 0
        percentile2_val = 0 
        
        cumulative_index = 0
        for val, length in self.val_nodes_length.items(): 
            cumulative_index += length
            
            if cumulative_index > self.percent_index1:
                break

            percentile1_val = val
        
        cumulative_index = 0
        for val, length in reversed(self.val_nodes_length.items()):
            cumulative_index += length
            
            if cumulative_index > self.percent_index2:
                break
            
            percentile2_val = val
        
        return percentile1_val, percentile2_val

    def __repr__(self):
        return f"Zset(nodes={self.nodes}, date_index={self.date_index})"


class PositionCalculator:
    """操作仓位计算器类，用于计算买入/卖出仓位"""
    def __init__(self, initial_cash=10000, max_position_ratio=3.0, min_position_ratio=0.2):
        """
        初始化操作仓位计算器
        
        参数:
        initial_cash: 初始现金单位，用于计算基础买入/卖出金额
        max_position_ratio: 最大仓位比例，用于限制单次操作的最大金额
        min_position_ratio: 最小仓位比例，用于确保信号较弱时仍有最小操作
        """
        self.initial_cash = initial_cash
        self.max_position_ratio = max_position_ratio
        self.min_position_ratio = min_position_ratio
    
    def calculate_buy_position(self, indicators):
        """
        计算买入仓位
        
        参数:
        indicators: 包含技术指标的字典，必须包含当前PE、PB以及它们的历史分位值
        
        返回:
        买入金额
        """
        # 提取必要的指标
        pe_current = indicators.get('pe_current')
        pb_current = indicators.get('pb_current')
        pe_chance = indicators.get('pe_chance')
        pb_chance = indicators.get('pb_chance')
        rsi = indicators.get('rsi', 50)  # 默认中性RSI值
        macd = indicators.get('macd', 0)
        
        if pe_current is None or pb_current is None or pe_chance is None or pb_chance is None:
            return 0
        
        # 计算PE和PB的偏离程度
        pe_deviation = pe_chance - pe_current
        pb_deviation = pb_chance - pb_current
        
        # 使用非线性函数放大信号，但不限制在[0,1]范围
        pe_signal = (math.exp(pe_deviation) - 1) if pe_deviation > 0 else 0
        pb_signal = (math.exp(pb_deviation) - 1) if pb_deviation > 0 else 0
        
        # 结合RSI指标，RSI低时(超卖)增强买入信号
        rsi_factor = max(0, (50 - rsi) / 50) if rsi < 50 else 0
        
        # 结合MACD指标，MACD为正时增强买入信号
        macd_factor = max(0, macd / 2) if macd > 0 else 0
        
        # 基础买入信号
        base_signal = 0.4 * pe_signal + 0.4 * pb_signal + 0.1 * rsi_factor + 0.1 * macd_factor
        
        # 应用非线性的信号转换，保证小信号有最小操作，大信号有放大效果
        if base_signal > 0:
            position_ratio = self.min_position_ratio + (self.max_position_ratio - self.min_position_ratio) * (1 - math.exp(-base_signal))
            return position_ratio * self.initial_cash
        
        return 0
    
    def calculate_sell_position(self, indicators, current_position=None):
        """
        计算卖出仓位
        
        参数:
        indicators: 包含技术指标的字典，必须包含当前PE、PB以及它们的危险分位值
        current_position: 当前持有的仓位金额，用于计算最大可卖出金额
        
        返回:
        卖出金额
        """
        # 提取必要的指标
        pe_current = indicators.get('pe_current')
        pb_current = indicators.get('pb_current')
        pe_danger = indicators.get('pe_danger')
        pb_danger = indicators.get('pb_danger')
        rsi = indicators.get('rsi', 50)  # 默认中性RSI值
        macd = indicators.get('macd', 0)
        
        if pe_current is None or pb_current is None or pe_danger is None or pb_danger is None:
            return 0
        
        # 计算PE和PB的偏离程度
        pe_deviation = pe_current - pe_danger
        pb_deviation = pb_current - pb_danger
        
        # 使用非线性函数放大信号，但不限制在[0,1]范围
        pe_signal = (math.exp(pe_deviation) - 1) if pe_deviation > 0 else 0
        pb_signal = (math.exp(pb_deviation) - 1) if pb_deviation > 0 else 0
        
        # 结合RSI指标，RSI高时(超买)增强卖出信号
        rsi_factor = max(0, (rsi - 50) / 50) if rsi > 50 else 0
        
        # 结合MACD指标，MACD为负时增强卖出信号
        macd_factor = max(0, -macd / 2) if macd < 0 else 0
        
        # 基础卖出信号
        base_signal = 0.4 * pe_signal + 0.4 * pb_signal + 0.1 * rsi_factor + 0.1 * macd_factor
        
        # 应用非线性的信号转换，保证小信号有最小操作，大信号有放大效果
        if base_signal > 0:
            position_ratio = self.min_position_ratio + (self.max_position_ratio - self.min_position_ratio) * (1 - math.exp(-base_signal))
            
            # 如果提供了当前仓位，确保不会超卖
            if current_position is not None:
                return min(position_ratio * self.initial_cash, current_position)
            
            return position_ratio * self.initial_cash
        
        return 0


def cal_index_pe_pb(code, start_date, end_date): 
    """计算指数的PE和PB数据"""
    index_data = get_price(code, start_date=start_date, end_date=end_date, frequency='daily', fields=None, skip_paused=False, fq='pre', count=None, panel=False, fill_paused=True)
    time_list = index_data.index.tolist()
    df = get_valuation(code, start_date=None, end_date=end_date, fields=['pe_ratio','pb_ratio','market_cap'], count=1)
    for date in time_list:
    #获取成份股
        stock_list = get_index_stocks(code, date=date)
        data = get_valuation(stock_list, start_date=None, end_date=date, fields=['pe_ratio','pb_ratio','market_cap'], count=1)
        df = pd.concat([df,data]).reset_index(drop=True)
    df = df.drop_duplicates()
    df['net_income_ttm'] = df['market_cap']/df['pe_ratio']
    df['book_value'] = df['market_cap']/df['pb_ratio']
    valuation = df.groupby('day').sum()
    valuation['pe_ttm_index'] = valuation['market_cap']/valuation['net_income_ttm']
    valuation['pb_ttm_index'] = valuation['market_cap']/valuation['book_value']
    return valuation


def get_technical_indicators(security_code, end_date, days=120):
    """获取技术指标数据"""
    # 获取历史价格数据
    price_data = get_price(security_code, end_date=end_date, count=days, frequency='daily')
    
    # 计算RSI指标（14日相对强弱指标）
    rsi = talib.RSI(price_data['close'].values, timeperiod=14)[-1]
    
    # 计算MACD指标
    macd, signal, hist = talib.MACD(
        price_data['close'].values, 
        fastperiod=12, 
        slowperiod=26, 
        signalperiod=9
    )
    
    # 返回最新的指标值
    return {
        'rsi': rsi,
        'macd': hist[-1],  # 使用MACD柱状图作为MACD指标值
        'close': price_data['close'][-1],
        'open': price_data['open'][-1],
        'high': price_data['high'][-1],
        'low': price_data['low'][-1],
        'volume': price_data['volume'][-1]
    }


# 初始化函数，设定基准等等
def initialize(context):
    # 设定基准
    set_benchmark('000300.XSHG')
    # 开启动态复权模式(真实价格)
    set_option("use_real_price", True)

    ### 场外基金相关设定 ###
    # 设置账户类型: 场外基金账户
    set_subportfolios([SubPortfolioConfig(context.portfolio.cash, 'open_fund')])
    # 设置赎回到账日
    set_redeem_latency(4, 'stock_fund')
    
    cur_date = context.current_dt
    
    first_start_date = cur_date - timedelta(days=5 * 365)
    first_start_date = first_start_date.strftime('%Y-%m-%d')
    
    # 维护沪深300历史PE，PB数据
    pe_pb_que = cal_index_pe_pb('000300.XSHG', first_start_date, cur_date)
    pe_pb_que = pe_pb_que[['pb_ttm_index', 'pe_ttm_index']]
    
    g.pe_cache = Zset(pe_pb_que['pe_ttm_index'])
    g.pb_cache = Zset(pe_pb_que['pb_ttm_index'])
    
    # 存储当前和历史指标值
    g.pe_today = None
    g.pb_today = None
    g.pe_danger = None
    g.pb_danger = None
    
    # 设置初始现金单位和仓位计算器
    allcash = context.portfolio.available_cash
    base_cash_unit = allcash / (5*12)  # 5年，按月频率计算基础值
    
    # 创建优化后的仓位计算器，设置最大仓位比例为3倍，最小为0.2倍
    g.position_calculator = PositionCalculator(
        initial_cash=base_cash_unit,
        max_position_ratio=3.0,
        min_position_ratio=0.2
    )
    
    # 运行函数
    run_daily(market_open, time='open', reference_security='000300.XSHG')
    run_daily(after_market_close, time='after_close', reference_security='000300.XSHG')


def market_open(context):
    """开盘时运行的函数，主要负责买入操作"""
    log.info("----------------------------------------")
    log.info("今天是周 %s" % context.current_dt.isoweekday())
    
    # 设置场外基金标的
    fund_code = '519671.OF'
    
    today = context.current_dt
    today_str = today.strftime('%Y-%m-%d')

    # 获取基金信息
    fund_info = get_fund_info(fund_code)
    log.info("基金名称：%s" % fund_info['fund_name'])

    # 获取沪深300的PE、PB数据
    pe_pb_ttm_today = cal_index_pe_pb('000300.XSHG', today, today)
    pe_pb_ttm_today = pe_pb_ttm_today[['pb_ttm_index', 'pe_ttm_index']]
    g.pb_today = pe_pb_ttm_today['pb_ttm_index'][0]
    g.pe_today = pe_pb_ttm_today['pe_ttm_index'][0]
    
    # 更新历史数据，删除5年前的数据
    drop_date = today - timedelta(days=5 * 365)
    drop_date_str = drop_date.strftime('%Y-%m-%d')
    
    node_pe = Node(today_str, g.pe_today)
    node_pb = Node(today_str, g.pb_today)
    
    g.pe_cache.insert(node_pe)
    g.pe_cache.delete(drop_date_str)
    
    g.pb_cache.insert(node_pb)
    g.pb_cache.delete(drop_date_str)
    
    # 获取历史分位值
    pe_chance, g.pe_danger = g.pe_cache.get_percentiles()
    pb_chance, g.pb_danger = g.pb_cache.get_percentiles()
    
    # 获取技术指标
    tech_indicators = get_technical_indicators('000300.XSHG', today)
    
    # 打印当前的指标信息
    log.info("%s PE: %.2f, PB: %.2f" % (today_str, g.pe_today, g.pb_today))
    log.info("PE_CHANCE: %.2f, PB_CHANCE: %.2f" % (pe_chance, pb_chance))
    log.info("RSI: %.2f, MACD: %.2f" % (tech_indicators['rsi'], tech_indicators['macd']))
    
    # 构建输入指标字典
    indicators = {
        'pe_current': g.pe_today,
        'pb_current': g.pb_today,
        'pe_chance': pe_chance,
        'pb_chance': pb_chance,
        'rsi': tech_indicators['rsi'],
        'macd': tech_indicators['macd'],
        'close': tech_indicators['close'],
        'volume': tech_indicators['volume']
    }
    
    # 计算买入仓位
    buy_amount = g.position_calculator.calculate_buy_position(indicators)
    
    if buy_amount > 100:  # 最小买入金额为100
        log.info("****************BUY**********************")
        log.info("买入金额: %.2f" % buy_amount)
        o = purchase(fund_code, buy_amount)
        log.info(o)


def after_market_close(context):
    """收盘后运行的函数，主要负责卖出操作"""
    log.info("----------------------------------------")
    
    # 设置场外基金标的
    fund_code = '519671.OF'
    today = context.current_dt
    today_str = today.strftime('%Y-%m-%d')
    
    # 打印当前的指标信息
    log.info("%s PE: %.2f, PB: %.2f" % (today_str, g.pe_today, g.pb_today))
    log.info("PE_DANGER: %.2f, PB_DANGER: %.2f" % (g.pe_danger, g.pb_danger))
    
    # 获取当前可赎回的数量
    closeable_amount = 0
    for f in context.portfolio.positions: 
        closeable_amount = context.portfolio.positions[f].closeable_amount
    
    # 获取技术指标
    tech_indicators = get_technical_indicators('000300.XSHG', today)
    log.info("RSI: %.2f, MACD: %.2f" % (tech_indicators['rsi'], tech_indicators['macd']))
    
    # 构建输入指标字典
    indicators = {
        'pe_current': g.pe_today,
        'pb_current': g.pb_today,
        'pe_danger': g.pe_danger,
        'pb_danger': g.pb_danger,
        'rsi': tech_indicators['rsi'],
        'macd': tech_indicators['macd'],
        'close': tech_indicators['close'],
        'volume': tech_indicators['volume']
    }
    
    # 计算卖出仓位
    sell_amount = g.position_calculator.calculate_sell_position(indicators, closeable_amount)
    
    if sell_amount > 100 and closeable_amount > 0:  # 最小卖出金额为100，且有可赎回份额
        log.info("****************SELL*************************")
        log.info("卖出金额: %.2f" % sell_amount)
        o = redeem(fund_code, sell_amount)
        log.info(o)
    
    # 查看融资融券账户相关信息
    p = context.portfolio.subportfolios[0]
    log.info('- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -')
    log.info('查看场外基金账户相关信息：')
    log.info('场外基金持有份额：%.2f' % p.long_positions['519671.OF'].closeable_amount)
    log.info('账户所属类型：%s' % p.type)
    log.info('##############################################################') 