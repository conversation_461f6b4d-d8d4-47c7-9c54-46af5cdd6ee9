import sys
import polars as pl
sys.path.append("/data/code")

from quant import cal_factor_ic, BaseFactor, init_factor

class VolumeAdvance(BaseFactor):

    '''helper function'''
    def _ema_span(self, df: pl.DataFrame, col_name: str, span: int=5):
        return df.with_columns((df[col_name].ewm_mean(span = span)).alias("tmp_result"))
    
    def _ma(self, df: pl.DataFrame, col_name: str, window: int):
        """计算简单移动平均线"""
        return df.with_columns((df[col_name].rolling_mean(window_size=window, min_periods=1)).alias("tmp_result"))

    def MACD(self, df: pl.DataFrame, short=12, long=26, m=9):
        """计算MACD指标"""
        # 计算短期EMA
        df = self._ema_span(df, col_name="close", span=short).rename({"tmp_result": "ema_short"})
        # 计算长期EMA
        df = self._ema_span(df, col_name="close", span=long).rename({"tmp_result": "ema_long"})
        # 计算DIF
        df = df.with_columns((pl.col("ema_short") - pl.col("ema_long")).alias("dif"))
        # 计算DEA（DIF的EMA）
        df = self._ema_span(df, col_name="dif", span=m).rename({"tmp_result": "dea"})
        # 计算MACD柱（DIF - DEA）
        df = df.with_columns((pl.col("dif") - pl.col("dea")).alias("macd_value"))
        return df

    def volume_ma_cross_coefficient(self, df: pl.DataFrame, short_window=5, long_window=10):
        """计算成交量MA交叉系数"""
        # 计算短期成交量MA
        df = self._ma(df, col_name="volume", window=short_window).rename({"tmp_result": "volume_ma_short"})
        # 计算长期成交量MA
        df = self._ma(df, col_name="volume", window=long_window).rename({"tmp_result": "volume_ma_long"})
        
        # 计算MA比值作为系数
        # 当短期MA > 长期MA时，系数 > 1，表示成交量上升趋势
        # 当短期MA < 长期MA时，系数 < 1，表示成交量下降趋势
        df = df.with_columns((pl.col("volume_ma_short") / pl.col("volume_ma_long")).alias("volume_coefficient"))
        
        return df

    def volume_advance_factor(self, df: pl.DataFrame, short_window=5, long_window=10, macd_short=12, macd_long=26, macd_m=9):
        """计算VolumeAdvance因子"""
        # 计算MACD
        df = self.MACD(df, short=macd_short, long=macd_long, m=macd_m)
        
        # 计算成交量MA交叉系数
        df = self.volume_ma_cross_coefficient(df, short_window=short_window, long_window=long_window)
        
        # 计算最终因子：成交量系数 * MACD值
        df = df.with_columns((pl.col("volume_coefficient") * pl.col("macd_value")).alias("factor"))
        
        return df

    def cal(self, df: pl.DataFrame):
        """主计算方法"""
        return self.volume_advance_factor(df)


init_factor(VolumeAdvance())

# 示例用法（注释掉，仅供参考）
# path_param = ["/", "data", "kline_okx", "perp", "30m"]
# all_ic_results, detailed_stats = cal_factor_ic(symbols=['BTC_USDT', 'TRUMP_USDT'], data_method = "param_li", param_li = path_param)
# print("\nIC值:")
# print(all_ic_results)
