import sys
import polars as pl
sys.path.append("/data/code")

from quant import cal_factor_ic, BaseFactor, init_factor

class VolumeAdvance(BaseFactor):

    '''helper function'''
    def _ema_span(self, df: pl.DataFrame, col_name: str, span: int=5):
        return df.with_columns((df[col_name].ewm_mean(span = span)).alias("tmp_result"))
    
    def _ma(self, df: pl.DataFrame, col_name: str, window: int):
        """计算简单移动平均线"""
        return df.with_columns((df[col_name].rolling_mean(window_size=window, min_periods=1)).alias("tmp_result"))

    def _rolling_max(self, df: pl.DataFrame, col_name: str, window: int):
        """计算滚动最大值"""
        return df.with_columns((df[col_name].rolling_max(window_size=window, min_periods=1)).alias("tmp_result"))

    def _rolling_min(self, df: pl.DataFrame, col_name: str, window: int):
        """计算滚动最小值"""
        return df.with_columns((df[col_name].rolling_min(window_size=window, min_periods=1)).alias("tmp_result"))

    def MACD(self, df: pl.DataFrame, short=12, long=26, m=9):
        """计算MACD指标"""
        # 计算短期EMA
        df = self._ema_span(df, col_name="close", span=short).rename({"tmp_result": "ema_short"})
        # 计算长期EMA
        df = self._ema_span(df, col_name="close", span=long).rename({"tmp_result": "ema_long"})
        # 计算DIF
        df = df.with_columns((pl.col("ema_short") - pl.col("ema_long")).alias("dif"))
        # 计算DEA（DIF的EMA）
        df = self._ema_span(df, col_name="dif", span=m).rename({"tmp_result": "dea"})
        # 计算MACD柱（DIF - DEA）
        df = df.with_columns((pl.col("dif") - pl.col("dea")).alias("macd_value"))
        return df

    def RSI(self, df: pl.DataFrame, period=14):
        """计算RSI指标"""
        # 计算价格变化
        df = df.with_columns((pl.col("close") - pl.col("close").shift(1)).alias("price_change"))

        # 计算上涨和下跌
        df = df.with_columns([
            pl.when(pl.col("price_change") > 0).then(pl.col("price_change")).otherwise(0).alias("gain"),
            pl.when(pl.col("price_change") < 0).then(-pl.col("price_change")).otherwise(0).alias("loss")
        ])

        # 计算平均上涨和平均下跌（使用EMA）
        df = self._ema_span(df, col_name="gain", span=period).rename({"tmp_result": "avg_gain"})
        df = self._ema_span(df, col_name="loss", span=period).rename({"tmp_result": "avg_loss"})

        # 计算RS和RSI
        df = df.with_columns((pl.col("avg_gain") / pl.col("avg_loss")).alias("rs"))
        df = df.with_columns((100 - (100 / (1 + pl.col("rs")))).alias("rsi"))

        return df

    def stochastic_RSI(self, df: pl.DataFrame, rsi_period=14, stoch_period=14, k_period=3, d_period=3):
        """计算随机RSI指标"""
        # 先计算RSI
        df = self.RSI(df, period=rsi_period)

        # 计算RSI的最高值和最低值
        df = self._rolling_max(df, col_name="rsi", window=stoch_period).rename({"tmp_result": "rsi_max"})
        df = self._rolling_min(df, col_name="rsi", window=stoch_period).rename({"tmp_result": "rsi_min"})

        # 计算%K值（原始随机RSI）
        df = df.with_columns(
            ((pl.col("rsi") - pl.col("rsi_min")) / (pl.col("rsi_max") - pl.col("rsi_min")) * 100).alias("stoch_rsi_raw")
        )

        # 计算%K值（%K的移动平均）
        df = self._ma(df, col_name="stoch_rsi_raw", window=k_period).rename({"tmp_result": "stoch_rsi_k"})

        # 计算%D值（%K的移动平均）
        df = self._ma(df, col_name="stoch_rsi_k", window=d_period).rename({"tmp_result": "stoch_rsi_d"})

        return df

    def volume_ma_cross_coefficient(self, df: pl.DataFrame, short_window=5, long_window=10):
        """计算成交量MA交叉系数"""
        # 计算短期成交量MA
        df = self._ma(df, col_name="volume", window=short_window).rename({"tmp_result": "volume_ma_short"})
        # 计算长期成交量MA
        df = self._ma(df, col_name="volume", window=long_window).rename({"tmp_result": "volume_ma_long"})
        
        # 计算MA比值作为系数
        # 当短期MA > 长期MA时，系数 > 1，表示成交量上升趋势
        # 当短期MA < 长期MA时，系数 < 1，表示成交量下降趋势
        df = df.with_columns((pl.col("volume_ma_short") / pl.col("volume_ma_long")).alias("volume_coefficient"))
        
        return df

    def volume_advance_factor(self, df: pl.DataFrame, mode="macd", short_window=5, long_window=10,
                             macd_short=12, macd_long=26, macd_m=9,
                             rsi_period=14, stoch_period=14, k_period=3, d_period=3,
                             combination_weight=0.5):
        """
        计算VolumeAdvance因子

        参数:
        mode: 计算模式
            - "macd": 仅使用MACD
            - "stoch_rsi": 仅使用随机RSI
            - "combined": MACD和随机RSI的加权组合
            - "macd_stoch_filter": 使用随机RSI过滤MACD信号
        combination_weight: 当mode="combined"时，MACD的权重（0-1之间）
        """
        # 计算成交量MA交叉系数
        df = self.volume_ma_cross_coefficient(df, short_window=short_window, long_window=long_window)

        if mode == "macd":
            # 仅使用MACD
            df = self.MACD(df, short=macd_short, long=macd_long, m=macd_m)
            df = df.with_columns((pl.col("volume_coefficient") * pl.col("macd_value")).alias("factor"))

        elif mode == "stoch_rsi":
            # 仅使用随机RSI
            df = self.stochastic_RSI(df, rsi_period=rsi_period, stoch_period=stoch_period,
                                   k_period=k_period, d_period=d_period)
            # 将随机RSI转换为类似MACD的信号（%K - %D）
            df = df.with_columns((pl.col("stoch_rsi_k") - pl.col("stoch_rsi_d")).alias("stoch_signal"))
            df = df.with_columns((pl.col("volume_coefficient") * pl.col("stoch_signal")).alias("factor"))

        elif mode == "combined":
            # MACD和随机RSI的加权组合
            df = self.MACD(df, short=macd_short, long=macd_long, m=macd_m)
            df = self.stochastic_RSI(df, rsi_period=rsi_period, stoch_period=stoch_period,
                                   k_period=k_period, d_period=d_period)
            df = df.with_columns((pl.col("stoch_rsi_k") - pl.col("stoch_rsi_d")).alias("stoch_signal"))

            # 加权组合
            combined_signal = (combination_weight * pl.col("macd_value") +
                             (1 - combination_weight) * pl.col("stoch_signal"))
            df = df.with_columns((pl.col("volume_coefficient") * combined_signal).alias("factor"))

        elif mode == "macd_stoch_filter":
            # 使用随机RSI过滤MACD信号
            df = self.MACD(df, short=macd_short, long=macd_long, m=macd_m)
            df = self.stochastic_RSI(df, rsi_period=rsi_period, stoch_period=stoch_period,
                                   k_period=k_period, d_period=d_period)

            # 当随机RSI处于超买（>80）或超卖（<20）区域时，减弱MACD信号
            stoch_filter = pl.when((pl.col("stoch_rsi_k") > 80) | (pl.col("stoch_rsi_k") < 20)).then(0.5).otherwise(1.0)
            df = df.with_columns((pl.col("volume_coefficient") * pl.col("macd_value") * stoch_filter).alias("factor"))

        else:
            raise ValueError(f"不支持的模式: {mode}. 支持的模式: 'macd', 'stoch_rsi', 'combined', 'macd_stoch_filter'")

        return df

    def cal(self, df: pl.DataFrame, mode="macd", **kwargs):
        """
        主计算方法

        参数:
        mode: 计算模式，默认为"macd"
        **kwargs: 传递给volume_advance_factor的其他参数
        """
        return self.volume_advance_factor(df, mode=mode, **kwargs)


init_factor(VolumeAdvance())

# 示例用法（注释掉，仅供参考）
# path_param = ["/", "data", "kline_okx", "perp", "30m"]
# all_ic_results, detailed_stats = cal_factor_ic(symbols=['BTC_USDT', 'TRUMP_USDT'], data_method = "param_li", param_li = path_param)
# print("\nIC值:")
# print(all_ic_results)
