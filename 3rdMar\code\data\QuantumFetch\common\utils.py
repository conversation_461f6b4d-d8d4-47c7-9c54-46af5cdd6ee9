import sys, time
sys.path.append("/data/code")
from core.http import *
from core.log import *
from core.bot import get_bot
import asyncio

async def async_retry_task(retry_queue: asyncio.Queue, outbound: asyncio.Queue):
    
    while True:
        task = await retry_queue.get()
        try:
            res = await task()
            if res is not None:
                await outbound.put(res)
            
            retry_queue.task_done()
        except Exception as e:
            print(f"Error executing task: {e}")
            await retry_queue.put(task)
            retry_queue.task_done()


