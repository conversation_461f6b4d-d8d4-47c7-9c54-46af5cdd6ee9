from jqdata import *
from datetime import datetime, timedelta
import pandas as pd
import math
from sortedcontainers import SortedDict
import numpy as np
import talib
import json

class QuantileCalculator:
    """分位值计算器（原Zset优化版）"""
    def __init__(self, series, config):
        self.nodes = SortedDict()
        self.val_nodes_length = SortedDict()
        self.total_nodes = 0
        self.date_index = {}
        self.percentile_low = config['percentile_low']
        self.percentile_high = config['percentile_high']
        
        for date, val in series.items():
            self._insert(Node(date, val))

    def _update_val_nodes_length(self, val, delta):
        self.val_nodes_length[val] = self.val_nodes_length.get(val, 0) + delta
        self.total_nodes += delta

    def _insert(self, node):
        if node.val not in self.nodes:
            self.nodes[node.val] = []
        self.nodes[node.val].append(node)
        self.date_index[node.date] = node
        self._update_val_nodes_length(node.val, 1)

    def update_series(self, new_series):
        for date, val in new_series.items():
            if date not in self.date_index:
                self._insert(Node(date, val))

    def get_quantiles(self):
        low_idx = int(self.percentile_low * self.total_nodes)
        high_idx = int((1 - self.percentile_high) * self.total_nodes)
        
        low_val = self._find_quantile(low_idx, reversed=False)
        high_val = self._find_quantile(high_idx, reversed=True)
        return low_val, high_val

    def _find_quantile(self, target_idx, reversed=False):
        cumulative = 0
        iterator = reversed(self.val_nodes_length.items()) if reversed else self.val_nodes_length.items()
        for val, count in iterator:
            cumulative += count
            if cumulative > target_idx:
                return val
        return 0

class PositionOptimizer:
    """仓位优化器（非线性计算模式）"""
    def __init__(self, config_path='config.json'):
        with open(config_path) as f:
            self.config = json.load(f)['position']
        
        self.base_unit = self.config['base_unit']
        self.max_ratio = self.config['max_ratio']
        self.min_ratio = self.config['min_ratio']
        self.risk_params = self.config['risk_params']

    def calculate_signal(self, indicators, position_type):
        """生成非线性交易信号"""
        signal = 0
        
        # 核心估值信号
        pe_signal = self._exp_signal(indicators['pe_deviation'])
        pb_signal = self._exp_signal(indicators['pb_deviation'])
        signal += self.risk_params['pe_weight'] * pe_signal
        signal += self.risk_params['pb_weight'] * pb_signal

        # 技术指标增强
        rsi_factor = self._rsi_enhancement(indicators['rsi'])
        macd_factor = self._macd_enhancement(indicators['macd'])
        signal += self.risk_params['rsi_weight'] * rsi_factor
        signal += self.risk_params['macd_weight'] * macd_factor

        # 波动率控制
        if 'volatility' in indicators:
            signal *= self._volatility_adjustment(indicators['volatility'])

        # 生成最终仓位
        ratio = self.min_ratio + (self.max_ratio - self.min_ratio) * (1 - math.exp(-signal))
        return ratio * self.base_unit * self._position_direction(position_type)

    def _exp_signal(self, deviation):
        return math.exp(max(deviation, 0)) - 1 if deviation > 0 else 0

    def _rsi_enhancement(self, rsi):
        return (50 - rsi)/50 if rsi < 50 else (rsi - 50)/50

    def _macd_enhancement(self, macd):
        return macd/2 if abs(macd) > 0.5 else 0

    def _volatility_adjustment(self, volatility):
        return 1 / (1 + math.log1p(volatility))

    def _position_direction(self, position_type):
        return 1 if position_type == 'long' else -1

class ConfigLoader:
    """动态配置加载器"""
    def __init__(self, config_path='config.json'):
        with open(config_path) as f:
            self.config = json.load(f)
        
    def get_quantile_config(self):
        return {
            'percentile_low': self.config['quantile']['low'],
            'percentile_high': self.config['quantile']['high'],
            'window_years': self.config['quantile']['window_years']
        }

    def get_risk_params(self):
        return self.config['risk_parameters']

# 初始化函数示例
"""
示例配置config.json内容：
{
    "quantile": {
        "low": 0.3,
        "high": 0.7,
        "window_years": 5
    },
    "position": {
        "base_unit": 10000,
        "max_ratio": 3.0,
        "min_ratio": 0.2,
        "risk_params": {
            "pe_weight": 0.4,
            "pb_weight": 0.4,
            "rsi_weight": 0.1,
            "macd_weight": 0.1
        }
    }
}
"""