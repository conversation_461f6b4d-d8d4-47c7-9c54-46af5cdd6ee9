import aiohttp
import asyncio
import polars as pl
import argparse
import time
import os
from datetime import datetime
from env_loader import TOKEN_INSIGHT_GET_COIN_HISTORY, get_api_headers

async def fetch_coin_history(coin_id, interval="day", length=-1, vs_currency="usd"):
    """
    异步获取币种历史数据

    参数:
    coin_id (str): 币种ID
    interval (str): 时间间隔，可选值为minute、hour、day，默认为day
    length (int): 返回的数据长度，-1表示最大值，默认为-1
    vs_currency (str): 计价货币，默认为usd

    返回:
    tuple: (polars.DataFrame, str, int, int, float)
           币种历史数据DataFrame、符号、首个时间戳、最后时间戳、当前市值
    """
    # 使用环境变量中的API URL
    url = f"{TOKEN_INSIGHT_GET_COIN_HISTORY}{coin_id}"
    params = {
        "interval": interval,
        "length": length,
        "vs_currency": vs_currency
    }

    # 获取包含API KEY的headers
    headers = get_api_headers()

    # 添加额外的headers，模拟requests库的行为
    headers.update({
        'User-Agent': 'python-requests/2.28.1',
        'Accept-Encoding': 'gzip, deflate',
        'Accept': '*/*',
        'Connection': 'keep-alive'
    })

    print(f"完整headers: {headers}")

    try:
        # 创建ClientSession时设置更多选项
        async with aiohttp.ClientSession(
            trust_env=True,  # 信任环境变量中的代理设置
            connector=aiohttp.TCPConnector(ssl=False)  # 禁用SSL验证，如果需要的话
        ) as session:
            async with session.get(url, params=params, headers=headers) as response:
                print(f"响应状态码: {response.status}")
                response.raise_for_status()
                data = await response.json()

                if data["status"]["code"] == 0:
                    history_data = data["data"]["market_chart"]
                    symbol = data["data"]["symbol"]

                    if not history_data:
                        print(f"警告: {coin_id} 没有历史数据")
                        return None, symbol, None, None, None

                    # 提取所需列
                    df = pl.DataFrame([{
                        "price": item.get("price"),
                        "vol_spot_24h": item.get("vol_spot_24h"),
                        "market_cap": item.get("market_cap"),
                        "timestamp": item.get("timestamp")
                    } for item in history_data])

                    # 获取首个和最后时间戳
                    timestamps = sorted([item.get("timestamp") for item in history_data])
                    first_timestamp = timestamps[0] if timestamps else None
                    last_timestamp = timestamps[-1] if timestamps else None

                    # 获取最新的市值数据
                    latest_data = sorted(history_data, key=lambda x: x.get("timestamp", 0), reverse=True)[0]
                    current_market_cap = latest_data.get("market_cap")

                    return df, symbol, first_timestamp, last_timestamp, current_market_cap
                else:
                    raise Exception(f"API调用错误: {data['status']['message']}")

    except Exception as e:
        print(f"获取 {coin_id} 历史数据时出错: {e}")
        return None, None, None, None, None

def save_to_parquet(df, symbol, coin_id, first_timestamp, last_timestamp, output_dir="./"):
    """
    将DataFrame保存为parquet文件

    参数:
    df (polars.DataFrame): 要保存的DataFrame
    symbol (str): 币种符号
    coin_id (str): 币种ID
    first_timestamp (int): 首个时间戳
    last_timestamp (int): 最后时间戳
    output_dir (str): 输出目录路径

    返回:
    str: 输出文件路径
    """
    try:
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        filename = f"{symbol}_{coin_id}_history_{first_timestamp}_{last_timestamp}.parquet"
        output_path = os.path.join(output_dir, filename)

        df.write_parquet(output_path)
        print(f"数据已保存到 {output_path}")
        return output_path
    except Exception as e:
        print(f"保存 {symbol}_{coin_id} 数据时出错: {e}")
        return None

async def async_main():
    parser = argparse.ArgumentParser(description="获取加密货币历史数据")
    parser.add_argument("--coin_id", type=str, required=True, help="币种ID")
    parser.add_argument("--interval", type=str, default="day", choices=["minute", "hour", "day"], help="时间间隔")
    parser.add_argument("--length", type=int, default=-1, help="返回的数据长度，-1表示最大值")
    parser.add_argument("--vs_currency", type=str, default="usd", help="计价货币")
    parser.add_argument("--output_dir", type=str, default="./", help="输出目录路径")

    args = parser.parse_args()

    # 异步获取币种历史数据
    df, symbol, first_timestamp, last_timestamp, current_market_cap = await fetch_coin_history(
        args.coin_id, args.interval, args.length, args.vs_currency
    )

    if df is not None and not df.is_empty():
        # 保存到parquet
        save_to_parquet(df, symbol, args.coin_id, first_timestamp, last_timestamp, args.output_dir)
    else:
        print(f"获取 {args.coin_id} 历史数据失败或返回空列表")

def main():
    """
    主函数，运行异步任务
    """
    asyncio.run(async_main())

if __name__ == "__main__":
    main()