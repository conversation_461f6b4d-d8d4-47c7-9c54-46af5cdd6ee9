import importlib
import yaml
import os

class CollectorRegistry:
    """
    Collector注册中心
    负责管理和注册Collector实现类
    """
    
    def __init__(self):
        """初始化CollectorRegistry"""
        self.collectors = {}
        self.data_sources = {}
    
    def register(self, config_path):
        """
        根据配置文件注册Collector
        
        参数:
        config_path (str): 配置文件路径
        """
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        if 'DataSource' not in config:
            raise ValueError("配置文件缺少DataSource部分")
        
        # 注册每个数据源的Collector
        for data_source in config['DataSource']:
            self.register_data_source(data_source)
    
    def register_data_source(self, data_source):
        """
        注册数据源及其Collector
        
        参数:
        data_source (dict): 数据源配置
        """
        if 'Name' not in data_source:
            raise ValueError("数据源配置缺少Name字段")
        
        name = data_source['Name']
        self.data_sources[name] = data_source
        
        if 'Collectors' in data_source:
            for collector_name in data_source['Collectors']:
                self.register_collector(collector_name, data_source)
    
    def register_collector(self, collector_name, data_source):
        """
        注册单个Collector
        
        参数:
        collector_name (str): Collector类名
        data_source (dict): 数据源配置
        """
        try:
            # 动态导入Collector类
            module_path = f"DataModule.Collectors.{collector_name.lower()}"
            module = importlib.import_module(module_path)
            collector_class = getattr(module, collector_name)
            
            # 创建Collector实例
            collector_instance = collector_class()
            
            # 注册Collector
            self.collectors[collector_name] = {
                'instance': collector_instance,
                'data_source': data_source['Name']
            }
            
            print(f"成功注册Collector: {collector_name}")
        except (ImportError, AttributeError) as e:
            print(f"注册Collector {collector_name} 失败: {e}")
    
    def get_collector(self, collector_name):
        """
        获取Collector实例
        
        参数:
        collector_name (str): Collector类名
        
        返回:
        Collector实例
        """
        if collector_name not in self.collectors:
            raise ValueError(f"未注册的Collector: {collector_name}")
        
        return self.collectors[collector_name]['instance']
    
    def get_all_collectors(self):
        """
        获取所有注册的Collector
        
        返回:
        dict: Collector字典
        """
        return self.collectors
    
    def get_data_source_collectors(self, data_source_name):
        """
        获取指定数据源的所有Collector
        
        参数:
        data_source_name (str): 数据源名称
        
        返回:
        list: Collector列表
        """
        return [
            collector_info['instance'] 
            for collector_name, collector_info in self.collectors.items() 
            if collector_info['data_source'] == data_source_name
        ]
