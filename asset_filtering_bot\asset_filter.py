import polars as pl
import os
import sys
import argparse

def filter_assets(file_path="./parquet_cache/collected_coins_interval.parquet", k=10):
    """
    对加密货币资产进行筛选，执行以下操作：
    1. 删除 current_market_cap 为 null 的记录
    2. 筛选出时间区间不重叠的记录，如果两个区间完全不重叠，舍弃 last_timestamp 较小的
    3. 根据 current_market_cap 排序，保留前 k 个记录
    
    参数:
        file_path: parquet 文件路径
        k: 保留的记录数量
        
    返回:
        筛选后的 DataFrame
    """
    # 读取 parquet 文件
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件 {file_path} 不存在")
    
    df = pl.read_parquet(file_path)
    
    # 打印读取的数据信息
    print(f"原始数据记录数: {len(df)}")
    print(f"字段: {df.columns}")
    
    # 1. 删除 current_market_cap 为 null 的记录
    df = df.filter(pl.col("current_market_cap").is_not_null())
    print(f"删除 current_market_cap 为 null 后的记录数: {len(df)}")
    
    # 2. 处理时间区间不重叠的情况
    if len(df) > 1:
        # 为数据添加临时索引列，便于后续处理
        df = df.with_row_index("temp_idx")
        
        # 按 symbol 分组处理数据
        symbols = df["symbol"].unique().to_list()
        all_to_keep = []
        
        print(f"处理 {len(symbols)} 个不同的加密货币符号...")
        
        for symbol in symbols:
            # 获取当前 symbol 的所有记录
            symbol_df = df.filter(pl.col("symbol") == symbol)
            
            if len(symbol_df) <= 1:
                # 如果只有一条记录，直接保留
                all_to_keep.extend(symbol_df["temp_idx"].to_list())
                continue
            
            # 转换为 Python 列表，方便处理区间
            intervals = []
            for row in symbol_df.iter_rows(named=True):
                intervals.append({
                    "idx": row["temp_idx"],
                    "first": row["first_timestamp"],
                    "last": row["last_timestamp"]
                })
            
            # 创建图结构，记录哪些区间相互不重叠
            # 图的键为区间索引，值为与该区间不重叠的所有区间索引列表
            non_overlap_graph = {i: [] for i in range(len(intervals))}
            
            # 构建图，复杂度 O(n²)，但通常 n 很小，因为同一个 symbol 的记录不会太多
            for i in range(len(intervals)):
                for j in range(i+1, len(intervals)):
                    # 检查两个区间是否完全不重叠: [a, b] 和 [c, d] 不重叠的条件是 b < c 或 d < a
                    if (intervals[i]["last"] < intervals[j]["first"] or 
                        intervals[j]["last"] < intervals[i]["first"]):
                        # 记录不重叠的关系（无向图）
                        non_overlap_graph[i].append(j)
                        non_overlap_graph[j].append(i)
            
            # 对于每个不重叠的区间对，保留 last_timestamp 大的那个
            to_remove = set()  # 用于存储要删除的区间索引
            
            for i in range(len(intervals)):
                if i in to_remove:
                    continue  # 跳过已经标记为删除的区间
                    
                for j in non_overlap_graph[i]:
                    if j in to_remove:
                        continue  # 跳过已经标记为删除的区间
                        
                    # 对于不重叠的区间对 (i, j)，保留 last 较大的那个
                    if intervals[i]["last"] < intervals[j]["last"]:
                        to_remove.add(i)
                        break  # i 已被标记删除，不需要继续检查
                    else:
                        to_remove.add(j)
            
            # 计算要保留的区间，并获取它们的原始索引
            to_keep = [intervals[i]["idx"] for i in range(len(intervals)) if i not in to_remove]
            all_to_keep.extend(to_keep)
        
        # 保留选定的记录
        df = df.filter(pl.col("temp_idx").is_in(all_to_keep))
        
        # 删除临时索引列
        df = df.drop("temp_idx")
        df = df.rename({"symbol": "token"})
        
        print(f"处理时间区间重叠后的记录数: {len(df)}")
    
    # 3. 按 current_market_cap 降序排序，选择前 k 条记录
    df = df.sort("current_market_cap", descending=True).head(k)
    print(f"选择 top {k} 条记录后的结果数: {len(df)}")
    
    # 返回结果
    return df

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="筛选加密货币资产")
    parser.add_argument("--input", "-i", type=str, 
                        default="./coins_mkc_history/collected_coins_interval.parquet",
                        help="输入的 parquet 文件路径")
    parser.add_argument("--output", "-o", type=str, 
                        default="./filtered_assets_top_200.parquet",
                        help="输出的 parquet 文件路径")
    parser.add_argument("--top", "-k", type=int, default=200,
                        help="保留的排名靠前的记录数量")
    
    return parser.parse_args()

def main():
    # 解析命令行参数
    args = parse_arguments()
    
    # 执行筛选
    try:
        result_df = filter_assets(args.input, args.top)
        
        # 打印结果
        print("\n筛选后的加密货币资产列表:")
        print(result_df)
        
        # 保存结果到 parquet 文件
        result_df.write_parquet(args.output)
        print(f"\n结果已保存到: {args.output}")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 