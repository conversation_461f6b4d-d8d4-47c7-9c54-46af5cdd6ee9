# X-GPT 爬虫工具

这是一个用于爬取 X-GPT 网站评论数据的工具，可以提取交易员关于各种加密资产的评论及其情感分析。

## 功能特点

- 支持两种抓取模式：
  - 直接从网站URL获取实时数据（推荐）
  - 从保存的HTML文件中提取数据
- 支持选择Chrome或Edge浏览器
- 专为vue-recycle-scroller动态加载组件优化
- 自动处理动态加载内容，获取更多评论
- 提取信息包括：资产名称、情感态度（积极、消极、中立）、评论时间、交易员名称
- 自动将数据保存为CSV格式，便于后续分析
- 智能识别评论中提及的加密货币符号
- 完善的错误处理机制
- 支持生成数据分析报告

## 使用依赖

```
beautifulsoup4
selenium
webdriver-manager
requests
```

在运行前，请确保安装了所需的依赖：

```bash
pip install beautifulsoup4 selenium webdriver-manager requests
```

## 使用方法

### 方法一：直接从URL抓取（推荐）

直接运行脚本，它将自动从默认URL获取数据：

```bash
python xgpt_scraper.py
```

或者指定要抓取的URL：

```bash
python xgpt_scraper.py https://x-gpt.bwequation.com/
```

### 方法二：从本地HTML文件提取

1. 使用浏览器访问 https://x-gpt.bwequation.com/
2. 使用 Ctrl+S 保存整个网页（保存为 "X-GPT.html"）
3. 运行脚本提取数据：

```bash
python xgpt_scraper.py X-GPT.html
```

## 命令行参数

脚本支持多种命令行参数，用于自定义爬取行为：

- `--scrolls`, `-s`: 滚动次数 (默认: 150)
- `--pause`, `-p`: 每次滚动后的等待时间，单位秒 (默认: 2.5)
- `--output`, `-o`: 输出CSV文件名 (默认: 当前日期.csv)
- `--max-no-new`, `-m`: 连续多少次滚动没有新评论后结束 (默认: 15)
- `--verbose`, `-v`: 显示详细输出
- `--report`, `-r`: 生成数据分析报告
- `--no-headless`, `-nh`: 非无头模式，显示浏览器窗口 (用于调试)
- `--browser`, `-b`: 选择使用的浏览器，可选 "chrome" 或 "edge" (默认: edge)

### 使用示例

使用Chrome浏览器抓取数据：
```bash
python xgpt_scraper.py --browser chrome
```

使用Edge浏览器，并显示浏览器窗口（用于调试）：
```bash
python xgpt_scraper.py --browser edge --no-headless
```

增加滚动次数和等待时间，提高数据收集量：
```bash
python xgpt_scraper.py --scrolls 200 --pause 3.0
```

生成数据分析报告：
```bash
python xgpt_scraper.py --report
```

## 工作原理

1. 当直接从URL抓取时，程序会：
   - 启动所选择的浏览器（Chrome或Edge）
   - 访问指定URL并等待页面加载
   - 识别并针对vue-recycle-scroller组件执行特定的滚动策略
   - 执行多次滚动操作，实时提取和保存内容
   - 关闭浏览器

2. 程序会智能分析页面内容，精确提取特定类名的元素：
   - 交易员名称 (class="name cursor-pointer")
   - 情感分析 (class="tweet-gpt-sentiment-xx")
   - 资产信息 ("Asset Involved:" 文本)

3. 结果保存为CSV文件，文件名使用当前日期（如2025.3.20.csv），并按时间排序

## 输出文件格式

输出的CSV文件包含以下列：

- asset: 资产名称（如BTC、ETH等）
- sentiment: 态度（positive、negative、neutral）
- time: 评论时间（格式为 HH:MM）
- trader: 交易员名称

示例：
```
asset,sentiment,time,trader
BTC,positive,19:36,RunnerXBT
ETH,negative,08:45,DonAlt
SOL,neutral,15:22,Pentoshi
```

## 注意事项

- 脚本会优先提取评论中明确标注的资产信息
- 对于未明确标注资产的评论，脚本会尝试从内容中识别常见的加密货币符号
- 只有包含有效资产信息的评论才会被记录到CSV文件中
- 时间提取使用的是评论的精确时间戳，而非相对时间（如"3小时前"）
- 直接从URL抓取模式需要安装选择的浏览器（Chrome或Edge）

## 问题排查

如果脚本无法正常工作，请检查：

1. 是否已安装所有依赖
2. 是否已安装所选择的浏览器（Chrome或Edge）
3. 网络连接是否正常
4. 使用本地文件时，文件编码是否为UTF-8

浏览器自动化相关问题：
- 如果遇到webdriver问题，可能需要手动下载与浏览器版本匹配的驱动程序
- 如果需要观察爬取过程，使用 `--no-headless` 参数可显示浏览器窗口
- 如果一种浏览器无法正常工作，尝试使用另一种浏览器 `--browser chrome` 或 `--browser edge`

如果遇到其他问题，请检查控制台输出的错误信息以获取更多线索。 