import sys
import polars as pl
sys.path.append("/data/code")

from quant import BaseFactor

# TODO: 加volume信号成为混合因子
class MACD_Volume(BaseFactor):
    
    def __init__(self, short=6, long=13, m=5, vol_span=5, to_date=False):
        self.short = short
        self.long = long
        self.m = m
        self.vol_span = vol_span
        self.to_date = to_date
        

    '''helper function'''
    def _convert_to_date(self, df: pl.DataFrame) -> pl.DataFrame:
        """将timestamp_in_micro转换为日期"""
        
        df = df.with_columns(
            pl.col("timestamp_in_micro").str.strptime(pl.Datetime, "%Y-%m-%d %H:%M:%S%z")
        )


        # TODO: 保留volume信息
        # 将数据按天降采样，取每日收盘价
        df_daily = df.sort("timestamp_in_micro").group_by_dynamic(
            "timestamp_in_micro",
            every="1d",
            period="1d",
            include_boundaries=False,
            closed="left"
        ).agg(
            pl.col("close").last().alias("close"),
            pl.col("vol").sum().alias("vol")
        )
        # 将 timestamp_in_micro 转换为日期类型
        df_daily = df_daily.with_columns(
            pl.col("timestamp_in_micro").dt.date().alias("date")
        )

        print("df_date",df_daily)
        return df_daily

    def _ema_span(self, df: pl.DataFrame, col_name: str, span: int=5):
        return df.with_columns((df[col_name].ewm_mean(span = span)).alias("tmp_result"))
    
    def _macd(self, df: pl.DataFrame, short=12, long=26, m=9):
        df = self._ema_span(df, col_name="close", span=short).rename({"tmp_result": "ema_short"})
        # 计算长期EMA（tmp_result_2）
        df = self._ema_span(df, col_name="close", span=long).rename({"tmp_result": "ema_long"})
        # 计算DIF并添加为列
        df = df.with_columns((pl.col("ema_short") - pl.col("ema_long")).alias("dif"))
        # 计算DEA（DIF的EMA）
        df = self._ema_span(df, col_name="dif", span=m).rename({"tmp_result": "dea"})
        # 计算MACD柱（DIF - DEA）
        df = df.with_columns(((pl.col("dif") - pl.col("dea"))).alias("macd"))

        # print(df)

        return df

    def _ema_volume(self, df: pl.DataFrame, span: int=5):
        return df.with_columns((df["vol"].ewm_mean(span = span)).alias("tmp_result")).rename({"tmp_result": "ema_vol"})

    def _macd_volume(self, df: pl.DataFrame, short=12, long=26, m=9, vol_span=5):
        df = df.pipe(self._macd, short, long, m).pipe(self._ema_volume, vol_span)

        df = df.with_columns((pl.col("vol") - pl.col("ema_vol")).alias("delta_vol"))
        df = df.with_columns((pl.col("delta_vol") + pl.col("macd")).alias("factor"))

        return df
    
    def cal(self, df: pl.DataFrame):
        print("df_raw: ", df)
        if self.to_date:
            df = df.pipe(self._convert_to_date)
        return self._macd_volume(df, self.short, self.long, self.m, self.vol_span)