import pandas as pd
from datetime import timedelta
import ccxt, time, glob
from tqdm import tqdm

def symbol_to_filename(symbol):
    return symbol.replace('/', '_').upper()

pd.set_option('expand_frame_repr', False)
pd.set_option('display.max_rows', 5000)


def get_ohlcv(exchange, savepath, symbol, time_interval, from_ts):
    cur_ts = int(time.time() * 1000)
    while True:
        ohlcv = exchange.fetch_ohlcv(symbol=symbol, timeframe=time_interval, since=from_ts, limit=100)
        if len(ohlcv) == 0:
            from_ts += 1000 * 60 * 60 * 24 * 30
            continue
        from_ts = ohlcv[-1][0]
        break
    pbar = tqdm(total=(cur_ts - from_ts),
                desc=f'Fetching {symbol} OHLCV',
                unit='ms')
    while True:
        data = exchange.fetch_ohlcv(symbol=symbol, timeframe=time_interval, since=from_ts, limit=100)
        last_ts = from_ts
        from_ts = data[-1][0]
        ohlcv.extend(data)
        pbar.update(from_ts - last_ts)
        if len(data) < 100:
        # if len(data) != 100 or True:
            pbar.update(cur_ts - from_ts)
            break
    pbar.close()

    df = pd.DataFrame(ohlcv, dtype=float)
    df.rename(columns={0: 'MTS', 1: 'open', 2: 'high',
                    3: 'low', 4: 'close', 5: 'volume'}, inplace=True)
    df['candle_begin_time'] = pd.to_datetime(df['MTS'], unit='ms')
    df['candle_begin_time_GMT8'] = df['candle_begin_time'] + timedelta(hours=8)
    df = df[['candle_begin_time_GMT8', 'open', 'high', 'low', 'close', 'volume']]
    df = df.drop_duplicates()
    df = df.sort_values("candle_begin_time_GMT8")
    print(df)
    df.to_parquet(f'{savepath}/{symbol_to_filename(symbol)}.parquet', index=False, engine='pyarrow', compression="zstd")
    # df.to_csv(f'{savepath}/{symbol_to_filename(symbol)}.csv')



ex = ccxt.okx()
symbol = 'YFI/USDT'
time_interval = '30m'  # '1m', '5m', '15m', '30m', '1h', '2h', '1d', '1w', '1M', '1y'
from_ts = ex.parse8601('2019-01-01 00:00:00')

''' single test '''
# get_ohlcv(ex, "/home/<USER>/dev/rio/tmp", symbol, time_interval, from_ts)
# exit(0)

type_di = {}
swap_li = []
spot_li = []
data = ex.fetch_markets()
for (i, d) in enumerate(data):
    if d['type'] not in type_di:
        type_di[d['type']] = 0
    if d['quote'] == 'USDT':
        type_di[d['type']] += 1
    if d['type'] == 'swap' and d['quote'] == 'USDT':
        swap_li.append(d['symbol'])
    if d['type'] == 'spot' and d['quote'] == 'USDT':
        spot_li.append(d['symbol'])
print(type_di)


''' perp '''
force_update = False
savepath = f"/data/cctx_okx/perp/{time_interval}"
exist_files = glob.glob(f"{savepath}/*.parquet")
for i, symbol in enumerate(swap_li):
    if not force_update and f"{savepath}/{symbol_to_filename(symbol)}.parquet" in exist_files:
        print(f"{i}/{len(swap_li)} {symbol} exists")
        continue
    print(f"{i}/{len(swap_li)} {symbol}")
    get_ohlcv(ex, savepath, symbol, time_interval, from_ts)


''' spot '''
# force_update = False
# savepath = f"/data/cctx_okx/spot/{time_interval}"
# exist_files = glob.glob(f"{savepath}/*.parquet")
# for i, symbol in enumerate(spot_li):
#     if not force_update and f"{savepath}/{symbol_to_filename(symbol)}.parquet" in exist_files:
#         print(f"{i}/{len(spot_li)} {symbol} exists")
#         continue
#     print(f"{i}/{len(spot_li)} {symbol}")
#     get_ohlcv(ex, savepath, symbol, time_interval, from_ts)
