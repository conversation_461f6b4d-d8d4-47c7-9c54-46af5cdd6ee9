DataSource:
  - Name: TokenInsight
    APIKey: xxxxxxxx
    Collectors:
      - TokenInsightCoinListCollector
      - TokenInsightCoinHistoryCollector
    Settings:
      workers: 12
      max_retries: 3
      api_limit: 60
      output_path: ./output/token_insight
      coins_cache_path: collected_coins.parquet
      symbol_list_path: symbol_list.csv
      coin_pool: 1500
      fetch_limit: 1500
      vs_currency: usd
      interval: day
      length: -1
      interval_output_path: collected_coins_interval.parquet

  - Name: CoinGecko
    APIKey: xxxxxxxx
    Collectors:
      - CoinGeckoCoinListCollector
      - CoinGeckoCoinHistoryCollector
    Settings:
      workers: 12
      max_retries: 3
      api_limit: 50
      output_path: ./output/coin_gecko
      coins_cache_path: collected_coins.parquet
      symbol_list_path: symbol_list.csv
      coin_pool: 1500
      fetch_limit: 250
      vs_currency: usd
      interval: daily
      length: -1
      interval_output_path: collected_coins_interval.parquet
