
我有如下数据格式：
┌────────────────────────┬────────┬────────┬────────┬────────┬────────────┐
│ candle_begin_time_GMT8 ┆ open   ┆ high   ┆ low    ┆ close  ┆ volume     │
│ ---                    ┆ ---    ┆ ---    ┆ ---    ┆ ---    ┆ ---        │
│ datetime[ns]           ┆ f64    ┆ f64    ┆ f64    ┆ f64    ┆ f64        │
╞════════════════════════╪════════╪════════╪════════╪════════╪════════════╡
│ 2019-01-01 08:00:00    ┆ 3701.4 ┆ 3701.8 ┆ 3688.5 ┆ 3695.1 ┆ 199.810361 │
│ 2019-01-01 08:30:00    ┆ 3694.0 ┆ 3712.9 ┆ 3693.5 ┆ 3702.2 ┆ 185.797509 

请你帮我设计一个时序模型，可以参考n-gram/ rnn等经典模型，模型输入n, n-1, n-2...n-w条数据，输出n时刻的价格是处于上涨趋势还是下跌趋势。价格趋势可以参考通过n+1时刻与n时刻做差判断，也可以由你自己设计更科学的判断方法。

现在你允许使用以下的包，不允许再install这里面不存在的包。
Package           Version
----------------- -----------
asttokens         2.0.5
comm              0.2.1
contourpy         1.3.1
cycler            0.12.1
debugpy           1.6.7
decorator         5.1.1
executing         0.8.3
fonttools         4.55.3
ipykernel         6.29.5
ipython           8.30.0
jedi              0.19.2
jupyter_client    8.6.0
jupyter_core      5.7.2
kiwisolver        1.4.8
matplotlib        3.10.0
matplotlib-inline 0.1.6
nest-asyncio      1.6.0
numpy             2.2.1
packaging         24.2
pandas            2.2.3
parso             0.8.4
pexpect           4.8.0
pillow            11.1.0
pip               24.2
platformdirs      3.10.0
polars            1.22.0
prompt-toolkit    3.0.43
psutil            5.9.0
ptyprocess        0.7.0
pure-eval         0.2.2
Pygments          2.15.1
pyparsing         3.2.1
python-dateutil   2.9.0.post0
pytz              2025.1
pyzmq             26.2.0
scipy             1.15.1
setuptools        75.1.0
six               1.16.0
stack-data        0.2.0
tornado           6.4.2
traitlets         5.14.3
typing_extensions 4.12.2
tzdata            2025.1
wcwidth           0.2.5
wheel             0.44.0

按照以上要求，帮我编写一套训练机器学习模型的代码框架。