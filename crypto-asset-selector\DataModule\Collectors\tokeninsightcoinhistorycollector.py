import os
import time
import requests
import polars as pl
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .base_collector import BaseCollector
from DataModule.Utils.env_loader import TOKEN_INSIGHT_GET_COIN_HISTORY, get_api_headers

class TokenInsightCoinHistoryCollector(BaseCollector):
    """TokenInsight币种历史数据收集器"""
    
    def __init__(self, 
                 workers: int = 12, 
                 max_retries: int = 3, 
                 api_limit: int = 60,
                 api_key: str = None,
                 interval_output_path: str = "DataModule/Cache/collected_coins_interval.parquet",
                 output_path: str = "DataModule/Cache/coins_history",
                 vs_currency: str = "usd"):
        """
        初始化TokenInsight币种历史数据收集器
        
        参数:
        workers (int): 线程池工作线程数
        max_retries (int): 最大重试次数
        api_limit (int): API每分钟请求数限制
        interval_output_path (str): 时间间隔数据输出文件路径
        output_path (str): 输出目录路径
        vs_currency (str): 计价货币
        """
        super().__init__(workers, max_retries, api_limit, api_key)
        self.interval_output_path = interval_output_path
        self.output_path = output_path
        self.vs_currency = vs_currency
    
    def pre_handle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理参数，准备输出目录并检查币种列表
        
        参数:
        params (Dict): 原始参数
        
        返回:
        Dict: 预处理后的参数
        """
        # 从参数中获取相关设置，如果没有则使用默认值
        params["interval_output_path"] = params.get("interval_output_path", self.interval_output_path)
        params["output_path"] = params.get("output_path", self.output_path)
        params["vs_currency"] = params.get("vs_currency", self.vs_currency)
        params["interval"] = params.get("interval", "day")  # 时间间隔，默认为天
        params["length"] = params.get("length", -1)  # 数据长度，默认为-1（全部数据）
        
        # 创建输出目录
        if not os.path.exists(params["output_path"]):
            os.makedirs(params["output_path"], exist_ok=True)
        
        # 添加当前时间戳到输出路径
        params["output_path"] = params["output_path"] + "_" + time.strftime("%Y%m%d_%H%M%S")
        if not os.path.exists(params["output_path"]):
            os.makedirs(params["output_path"], exist_ok=True)
        
        # 检查是否提供了币种列表
        if "coins" not in params or not params["coins"]:
            raise ValueError("未提供币种列表")
        
        return params
    
    def handle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理币种历史数据获取
        
        参数:
        params (Dict): 预处理后的参数
        
        返回:
        Dict: 处理结果
        """
        result = {
            "success": False,
            "data": None,
            "message": ""
        }
        
        try:
            # 提取币种列表
            coins_data = params["coins"]
            
            # 准备任务列表
            tasks = []
            for coin in coins_data:
                task = {
                    "coin_id": coin["id"],
                    "symbol": coin["symbol"],
                    "interval": params["interval"],
                    "length": params["length"],
                    "vs_currency": params["vs_currency"],
                    "output_path": params["output_path"],
                    "retry_count": 0
                }
                tasks.append(task)
            
            # 使用线程池处理
            processed_results = self.execute_with_threadpool(self._process_coin, tasks)
            
            # 收集结果
            successful = sum(1 for r in processed_results if r and r.get("success", False))
            
            # 保存间隔数据
            interval_data_path = os.path.join(params["output_path"], os.path.basename(params["interval_output_path"]))
            self._save_interval_data(interval_data_path)
            
            result["success"] = True
            result["data"] = {
                "total": len(tasks),
                "successful": successful,
                "interval_data_path": interval_data_path,
                "output_path": params["output_path"]
            }
            result["message"] = f"成功获取 {successful}/{len(tasks)} 个币种的历史数据"
            
        except Exception as e:
            result["message"] = f"获取币种历史数据出错: {e}"
        
        return result
    
    def post_handle(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        后处理结果
        
        参数:
        result (Dict): 处理结果
        
        返回:
        Dict: 后处理后的结果
        """
        # 如果有需要的额外处理可以在这里添加
        return result
    
    def _process_coin(self, coin_id: str, symbol: str, interval: str, length: int, 
                     vs_currency: str, output_path: str, retry_count: int = 0) -> Dict[str, Any]:
        """
        处理单个币种的历史数据
        
        参数:
        coin_id (str): 币种ID
        symbol (str): 币种符号
        interval (str): 时间间隔
        length (int): 数据长度
        vs_currency (str): 计价货币
        output_path (str): 输出目录路径
        retry_count (int): 重试次数
        
        返回:
        Dict: 处理结果
        """
        result = {
            "success": False,
            "symbol": symbol,
            "coin_id": coin_id,
            "message": ""
        }
        
        print(f"正在获取 {symbol}({coin_id}) 的历史数据..." + (f" 重试 #{retry_count}" if retry_count > 0 else ""))
        
        # 检查并遵守API请求限制
        self.check_and_update_rate_limit()
        
        try:
            # 获取历史数据
            df, symbol, first_timestamp, last_timestamp, current_market_cap = self._fetch_coin_history(
                coin_id, interval, length, vs_currency
            )
            
            if df is not None and not df.is_empty():
                # 构建输出路径
                output_file = f"{symbol}_{coin_id}_history_{first_timestamp}_{last_timestamp}.parquet"
                full_output_path = os.path.join(output_path, output_file)
                
                # 保存历史数据
                df.write_parquet(full_output_path)
                
                # 更新间隔数据
                self.add_to_data_queue({
                    "symbol": symbol,
                    "id": coin_id,
                    "first_timestamp": first_timestamp,
                    "last_timestamp": last_timestamp,
                    "current_market_cap": current_market_cap,
                    "file_path": full_output_path
                })
                
                result["success"] = True
                result["message"] = f"成功获取 {symbol}({coin_id}) 的历史数据"
                return result
            else:
                result["message"] = f"未获取到 {symbol}({coin_id}) 的历史数据或返回空数据"
        
        except Exception as e:
            result["message"] = f"获取 {symbol}({coin_id}) 的历史数据时出错: {e}"
        
        # 如果失败且未达到最大重试次数，则加入重试队列
        if retry_count < self.max_retries:
            self.retry_queue.put({
                "coin_id": coin_id,
                "symbol": symbol,
                "interval": interval,
                "length": length,
                "vs_currency": vs_currency,
                "output_path": output_path,
                "retry_count": retry_count
            })
        
        return result
    
    def _fetch_coin_history(self, coin_id: str, interval: str = "day", 
                           length: int = -1, vs_currency: str = "usd") -> Tuple[Optional[pl.DataFrame], str, int, int, float]:
        """
        获取币种历史数据
        
        参数:
        coin_id (str): 币种ID
        interval (str): 时间间隔，可选值为minute、hour、day，默认为day
        length (int): 返回的数据长度，-1表示最大值，默认为-1
        vs_currency (str): 计价货币，默认为usd
        
        返回:
        Tuple: (pl.DataFrame, str, int, int, float) 包含DataFrame、符号、首个时间戳、最后时间戳、当前市值
        """
        # 使用环境变量中的API URL
        url = f"{TOKEN_INSIGHT_GET_COIN_HISTORY}{coin_id}"
        params = {
            "interval": interval,
            "length": length,
            "vs_currency": vs_currency
        }
        
        # 获取包含API KEY的headers
        headers = get_api_headers()
        
        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            if data["status"]["code"] == 0:
                history_data = data["data"]["market_chart"]
                symbol = data["data"]["symbol"]
                
                if not history_data:
                    print(f"警告: {coin_id} 没有历史数据")
                    return None, symbol, None, None, None
                
                # 提取所需列
                df = pl.DataFrame([{
                    "price": item.get("price"),
                    "vol_spot_24h": item.get("vol_spot_24h"),
                    "market_cap": item.get("market_cap"),
                    "timestamp": item.get("timestamp")
                } for item in history_data])
                
                # 获取首个和最后时间戳
                timestamps = sorted([item.get("timestamp") for item in history_data])
                first_timestamp = timestamps[0] if timestamps else None
                last_timestamp = timestamps[-1] if timestamps else None
                
                # 获取最新的市值数据
                latest_data = sorted(history_data, key=lambda x: x.get("timestamp", 0), reverse=True)[0]
                current_market_cap = latest_data.get("market_cap")
                
                return df, symbol, first_timestamp, last_timestamp, current_market_cap
            else:
                raise Exception(f"API调用错误: {data['status']['message']}")
        
        except Exception as e:
            print(f"获取 {coin_id} 历史数据时出错: {e}")
            return None, None, None, None, None
    
    def _save_interval_data(self, output_path: str) -> None:
        """
        保存币种时间间隔数据
        
        参数:
        output_path (str): 输出文件路径
        """
        if not self.collected_data:
            print("没有间隔数据可保存")
            return
        
        # 创建输出目录（如果不存在）
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        df = pl.DataFrame(self.collected_data)
        df.write_parquet(output_path)
        print(f"币种时间间隔数据已保存到 {output_path}")
        print(f"收集了 {len(self.collected_data)} 个币种的时间间隔数据") 