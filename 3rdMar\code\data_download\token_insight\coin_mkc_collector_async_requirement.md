## 一、任务概述

现在我有两个API：

1. coin_list_fetcher.py

2. coin_history_fetcher.py

我希望先使用coin_list_fetcher.py里的API获取所有可用的币种的id和symbol，并通过symbol列与本地的文件symbol_list.csv做一个交集，筛选出一部分币种的id，然后使用这个id的合集，用里面的每一个id调用coin_history_fetcher.py里的API，获取每一个选中的币的历史数据。


## 二、具体要求

（考虑到之后这个脚本可能集成到选股机器人的数据筛选模块中）

我希望使用python编写脚本，完成数据的获取

脚本调用上面两个文件内的方法。如果本地已经存在collected_coins.parquet,则优先使用本地缓存获取id和symbol；否则先使用coin_list_fetcher.py里的方法获取所有可用的币种的id和symbol（每次请求最多1500个币种，coinpool有15704个币种，所以通过offset来控制每一个请求的位置，通过多个请求获取所有id和symbol），并通过symbol列与本地的文件symbol_list.csv做一个交集，筛选出一部分币种的id，然后使用这个id的合集，用里面的每一个id调用coin_history_fetcher.py里的方法，获取每一个选中的币的历史数据，query param里的length直接选-1，获取最大值（本过程要求使用aysnc IO，通过协程进行网络并发，并留好并发的参数设置接口）。

调用coin_history_fetcher.py里的方法获取每个资产的历史数据时，注意设置API请求频率限制，避免触发限流。同时，对失败的请求，建立动态重试机制，不要等所有任务都跑完再统一重试，最多重试3次。

在调用coin_history_fetcher.py里的方法时，同时记录每一个symbol的起始timestamp和结束timestamp（如果获取数据时使用了协程并引发了资源竞争，请你自行设计算法解决竞争问题，可以参考blockingque或者concurrent hash map之类的方法，尽量不要因为颗粒度太大的锁影响多线程性能，若不存在竞争问题则不用管），把这个信息存到一张新的表collected_coins_interval.parquet中，表内含有symbol，id，start_timestamp, end_timestamp, current_market_cap5列。

数据的读写、变形操作尽量使用polars库

