import os
import argparse
import yaml
from collector_registry import CollectorRegistry

def load_config(config_path):
    """
    加载配置文件
    
    参数:
    config_path (str): 配置文件路径
    
    返回:
    dict: 配置信息
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    return config

def run_data_collection(config_path, data_source=None):
    """
    运行数据收集流程
    
    参数:
    config_path (str): 配置文件路径
    data_source (str, optional): 指定要运行的数据源，如果为None则运行所有数据源
    """
    # 加载配置
    config = load_config(config_path)
    
    # 创建注册中心
    registry = CollectorRegistry()
    registry.register(config_path)
    
    # 确定要运行的数据源
    data_sources = []
    if data_source:
        # 运行指定的数据源
        for ds in config['DataSource']:
            if ds['Name'] == data_source:
                data_sources.append(ds)
                break
        if not data_sources:
            raise ValueError(f"未找到数据源: {data_source}")
    else:
        # 运行所有数据源
        data_sources = config['DataSource']
    
    # 运行每个数据源的收集器
    for ds in data_sources:
        print(f"开始处理数据源: {ds['Name']}")
        
        # 获取数据源的设置
        settings = ds.get('Settings', {})
        
        # 运行CoinListCollector
        coin_list_collector_name = f"{ds['Name']}CoinListCollector"
        try:
            coin_list_collector = registry.get_collector(coin_list_collector_name)
            print(f"运行 {coin_list_collector_name}...")
            coins_data = coin_list_collector.run(settings)
            
            if coins_data is not None:
                # 提取币种ID和符号
                coins_list = coins_data.select(["id", "symbol"]).to_dicts()
                
                # 运行CoinHistoryCollector
                coin_history_collector_name = f"{ds['Name']}CoinHistoryCollector"
                try:
                    coin_history_collector = registry.get_collector(coin_history_collector_name)
                    print(f"运行 {coin_history_collector_name}...")
                    
                    # 将币种列表数据传递给历史数据收集器
                    history_params = settings.copy()
                    history_params['coins_data'] = coins_list
                    
                    coin_history_collector.run(history_params)
                except (ValueError, KeyError) as e:
                    print(f"运行 {coin_history_collector_name} 失败: {e}")
            else:
                print(f"{coin_list_collector_name} 未返回有效的币种列表数据")
        except (ValueError, KeyError) as e:
            print(f"运行 {coin_list_collector_name} 失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="加密货币数据收集模块")
    parser.add_argument("--config", type=str, default="data_config.yaml", help="配置文件路径")
    parser.add_argument("--data_source", type=str, help="指定要运行的数据源")
    
    args = parser.parse_args()
    
    run_data_collection(args.config, args.data_source)

if __name__ == "__main__":
    main()
