一、简介
hs300.py是jointquant在线量化平台上的一段回测程序，仅可以在在线回测平台运行。这段程序是沪深300基金的定投程序，基本功能是收集并计算5年内的沪深300的价格，PB，PE这几个指标，并维护PB、PE两个指标的历史分位值（即机会值与危险值）。并通过一定方法将当前日期的PB、PE值纳入买入卖出策略。

具体的来说，当前的买入策略是计算当日PB或者PE与机会值的差，并映射到指数函数上，然后通过sigMoid函数加权确定一个0-1之间的值add_position，作为定投的增幅。最后用（1+add_position）* cash_unit得到当日加仓的数值。

而卖出策略也是类似，只不过是用当日PB或PE与危险值的差作为变量。

二、现存问题
这个方法我认为有以下问题：

1. 不知道如何界定一个合理的cash_unit，如果太小，在信号来的时候会跟不上市场，如果太大，会被假信号一次骗光仓位；

2. 买入卖出策略的变量不合理，使用指数+sigmoid加权后的add_position永远被限制在0-1之间，也就是说每日买卖的仓位最多是0-2*cash_unit，在行情波动大时反应不够迅速。

三、任务要求
现在，我希望你优化这个策略。我的需求是：

1. 不要引入jointquant的一些自动选股之类的API，因为我要手动进行交易，而不会使用机器交易。理想的交易过程是：（1）我在各大券商、金融平台浏览到沪深300当日的一些技术指标值；（2）我将这些值输入你设计好的操作仓位计算器中，得到今日需要操作的仓位值；（3）我在相应的券商账户上手动操作交易。

2. 我希望你将这个策略设计成一个输入为技术指标，输出为买入/卖出仓位数量的函数，并且这个函数最好不要依赖任何jointquant的API，只要依赖python的开源库就好，以便我将这个函数从回测框架中剥离出来作为操作仓位计算器使用。

3. 可以接受的技术指标有：MA, BOLL, EMA, SAR, KC, 一目均衡表，VWAP，神奇九转以及成交量, MACD, KDJ, ARBR, CR, DMA, EMV, RSI, PE, PB, PE历史分位值, PB历史分位值, 风险溢价, 开盘价, 收盘价, 高位, 低位。它们都是我可以直接从券商APP上肉眼获取的数据。请你根据你所知道的沪深300指数的市场特性，选择合适的指标作为函数的输入。

4. 至于仓位的计算过程，不要引入除了以上输入之外的统计量（如擅自抽取多日内的价格制成一个dataframe并完成一些如均值等操作），牢记我需要你将策略设计成一个操作仓位计算器，计算器的输入和输出的格式是确定的，我无法提供多日的某个数值。此外，尽可能的克服二、现存问题中提到的一些缺陷。

5. 将符合以上要求的操作仓位计算器封装成一个类，在jointquant平台上进行在线回测，在这个类之外你可以随意调用jointquant的API，参考hs300.py里的写法。

6. 仅编写代码，不要尝试在本地运行或者安装任何环境。因为这段代码仅适用于jointquant在线回测平台，且本地也并未安装python环境。
