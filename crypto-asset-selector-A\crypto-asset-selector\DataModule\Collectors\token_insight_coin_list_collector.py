import os
import polars as pl
import time
import requests
from queue import Queue
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor

from .base_collector import BaseCollector
from ..Utils.env_loader import TOKEN_INSIGHT_GET_COIN_LIST, get_api_headers

class TokenInsightCoinListCollector(BaseCollector):
    """TokenInsight币种列表数据收集器"""

    def fetch_coin_list(self, limit=1500, offset=0, vs_currency="usd"):
        """
        获取币种列表数据

        参数:
        limit (int): 返回的币种数量，默认1500，最大1500
        offset (int): 偏移量，默认0
        vs_currency (str): 计价货币，默认为usd

        返回:
        polars.DataFrame: 包含币种信息的DataFrame
        """
        # 使用环境变量中的API URL
        url = TOKEN_INSIGHT_GET_COIN_LIST
        params = {
            "limit": limit,
            "offset": offset,
            "vs_currency": vs_currency
        }

        # 获取包含API KEY的headers
        headers = get_api_headers()

        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data["status"]["code"] == 0:
                coins_data = data["data"]["items"]

                # 提取所需列
                df = pl.DataFrame([{
                    "price": coin.get("price"),
                    "symbol": coin.get("symbol"),
                    "id": coin.get("id"),
                    "spot_volume_24h": coin.get("spot_volume_24h"),
                    "price_change_percentage_24h": coin.get("price_change_percentage_24h")
                } for coin in coins_data])

                return df
            else:
                raise Exception(f"API调用错误: {data['status']['message']}")

        except Exception as e:
            print(f"获取币种列表时出错: {e}")
            return None

    def save_to_parquet(self, df, output_path="collected_coins.parquet"):
        """
        将DataFrame保存为parquet文件

        参数:
        df (polars.DataFrame): 要保存的DataFrame
        output_path (str): 输出文件路径
        """
        try:
            df.write_parquet(output_path)
            print(f"数据已保存到 {output_path}")
        except Exception as e:
            print(f"保存数据时出错: {e}")

    def filter_by_symbol_list(self, df, symbol_list_path="symbol_list.csv"):
        """
        通过symbol_list.csv过滤币种

        参数:
        df (polars.DataFrame): 要过滤的DataFrame
        symbol_list_path (str): symbol列表文件路径

        返回:
        polars.DataFrame: 过滤后的DataFrame
        """
        try:
            if not os.path.exists(symbol_list_path):
                print(f"错误: 未找到符号列表文件 {symbol_list_path}")
                return df

            symbol_df = pl.read_csv(symbol_list_path)
            # 过滤币种
            filtered_df = df.filter(pl.col("symbol").is_in(symbol_df["symbol"]))

            print(f"通过符号列表过滤后，币种数量从 {len(df)} 减少到 {len(filtered_df)}")
            return filtered_df

        except Exception as e:
            print(f"过滤币种时出错: {e}")
            return df

    def __init__(self,
                 workers=12,
                 max_retries=3,
                 api_limit=60,
                 output_path="./output",
                 coins_cache_path="collected_coins.parquet",
                 symbol_list_path="symbol_list.csv",
                 coin_pool=1500,
                 fetch_limit=1500,
                 vs_currency="usd"):
        """
        初始化TokenInsightCoinListCollector

        参数:
        workers (int): 线程池工作线程数
        max_retries (int): 最大重试次数
        api_limit (int): API请求限制（每分钟）
        output_path (str): 输出数据存放目录
        coins_cache_path (str): 币种缓存文件路径
        symbol_list_path (str): 符号列表文件路径
        coin_pool (int): 要获取的最大币种数量
        fetch_limit (int): 每次API请求获取的币种数量
        vs_currency (str): 计价货币
        """
        super().__init__(workers, max_retries, api_limit, output_path)
        self.coins_cache_path = os.path.join(self.output_path, coins_cache_path)
        self.symbol_list_path = symbol_list_path
        self.coin_pool = min(coin_pool, 16000)  # 最大支持约16000个币种
        self.fetch_limit = min(fetch_limit, 1500)  # 单次请求最大1500个币种
        self.vs_currency = vs_currency

    def pre_handle(self, params):
        """
        预处理阶段，检查参数并准备环境

        参数:
        params (dict): 运行参数
        """
        # 更新参数（如果提供）
        if params:
            if 'coin_pool' in params:
                self.coin_pool = min(params['coin_pool'], 16000)
            if 'fetch_limit' in params:
                self.fetch_limit = min(params['fetch_limit'], 1500)
            if 'vs_currency' in params:
                self.vs_currency = params['vs_currency']
            if 'symbol_list_path' in params:
                self.symbol_list_path = params['symbol_list_path']
            if 'coins_cache_path' in params:
                self.coins_cache_path = os.path.join(self.output_path, params['coins_cache_path'])

    def get_tasks(self, params):
        """
        获取任务列表

        参数:
        params (dict): 运行参数

        返回:
        list: 任务列表，每个任务是一个(limit, offset, batch_num)元组
        """
        # 如果本地已缓存，直接读取并过滤
        if os.path.exists(self.coins_cache_path):
            print(f"使用本地缓存的币种列表: {self.coins_cache_path}")
            df = pl.read_parquet(self.coins_cache_path)
            # 对缓存的数据也进行过滤
            filtered_df = self.filter_by_symbol_list(df, self.symbol_list_path)
            # 将结果保存在实例变量中，供post_handle使用
            self.result_df = filtered_df
            return []  # 没有需要处理的任务

        # 否则，分批次获取币种列表
        print(f"开始获取币种列表，目标数量: {self.coin_pool}")

        tasks = []
        offset = 0
        batches_count = 0

        # 计算需要获取的批次数
        total_batches = (self.coin_pool + self.fetch_limit - 1) // self.fetch_limit

        while offset < self.coin_pool:
            batches_count += 1
            # 计算当前批次应获取的数量
            current_limit = min(self.fetch_limit, self.coin_pool - offset)

            # 添加任务
            tasks.append((current_limit, offset, batches_count))

            # 更新偏移量
            offset += self.fetch_limit

        return tasks

    def handle(self, params):
        """
        主处理阶段，执行核心业务逻辑

        参数:
        params (dict): 运行参数

        返回:
        处理结果
        """
        # 如果已经在get_tasks中处理了缓存数据
        if hasattr(self, 'result_df'):
            return self.result_df

        # 否则调用父类的handle方法，使用并发处理任务
        self.all_coins_df = None  # 初始化结果DataFrame
        return super().handle(params)

    def post_handle(self, result):
        """
        后处理阶段，处理获取到的币种列表数据

        参数:
        result: 处理结果，可能是DataFrame或handle方法返回的字典

        返回:
        polars.DataFrame: 处理后的币种列表数据
        """
        # 如果已经在get_tasks中处理了缓存数据
        if hasattr(self, 'result_df'):
            return self.result_df

        # 如果是handle方法返回的字典结果
        if isinstance(result, dict):
            # 检查是否有收集到的数据
            if hasattr(self, 'all_coins_df') and self.all_coins_df is not None and not self.all_coins_df.is_empty():
                print(f"共获取 {len(self.all_coins_df)} 个币种")

                # 过滤并保存
                filtered_df = self.filter_by_symbol_list(self.all_coins_df, self.symbol_list_path)
                self.save_to_parquet(filtered_df, self.coins_cache_path)
                return filtered_df
            else:
                print("未获取到币种列表数据")
                return None

        # 如果是DataFrame结果
        if result is None or (hasattr(result, 'is_empty') and result.is_empty()):
            print("未获取到符合条件的币种列表")
            return None

        print(f"获取到 {len(result)} 个符合条件的币种")
        return result

    def process_task(self, task):
        """
        处理单个任务（重试队列中的任务）

        参数:
        task (tuple): (limit, offset, batch_num)

        返回:
        tuple: (bool, DataFrame, batch_num) 是否成功、数据、批次号
        """
        limit, offset, batch_num = task
        # 检查并遵守API请求限制
        self.check_and_update_rate_limit()

        # 获取当前批次的币种列表
        batch_df = self.fetch_coin_list(limit=limit, offset=offset, vs_currency=self.vs_currency)

        if batch_df is None or batch_df.is_empty():
            print(f"获取币种批次 {batch_num} 失败或返回空列表")
            return False, None, batch_num

        print(f"批次 {batch_num} 获取到 {len(batch_df)} 个币种")

        # 合并数据到全局结果
        if not hasattr(self, 'all_coins_df') or self.all_coins_df is None:
            self.all_coins_df = batch_df
        else:
            self.all_coins_df = pl.concat([self.all_coins_df, batch_df])

        return True, batch_df, batch_num

    def _save_interval_data_impl(self):
        """保存中间数据的具体实现"""
        # 对于币种列表收集器，不需要保存中间数据
        pass
