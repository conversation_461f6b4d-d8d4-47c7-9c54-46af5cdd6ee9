# 加密货币数据收集模块 (DataModule)

这个模块使用模板方法模式和装饰器模式，为不同数据源的API设计了一个通用的数据收集框架。当前版本实现了TokenInsight API的币种列表和历史数据收集功能。

## 项目结构

```
DataModule/
├── Cache/                     # 缓存目录，存放中间产物数据
├── Collectors/                # 收集器实现目录
│   ├── base_collector.py      # 基础收集器类，实现模板方法模式
│   ├── collector_registry.py  # 收集器注册中心
│   ├── tokeninsightcoinhistorycollector.py  # TokenInsight历史数据收集器
│   └── tokeninsightcoinlistcollector.py     # TokenInsight币种列表收集器
├── Utils/                     # 工具类目录
│   └── env_loader.py          # 环境变量加载工具
├── .data.env                  # 数据源API配置
├── data_config.yaml           # 数据模块配置
├── main.py                    # 入口文件
└── README.md                  # 项目说明文件
```

## 运行方式

### 基本用法

```bash
python DataModule/main.py
```

默认使用`DataModule/data_config.yaml`中的配置。

### 高级用法

```bash
python DataModule/main.py --workers 12 --max_retries 3 --output_path "output/coins_history" --symbol_list "symbol_list.csv" --vs_currency "usd" --interval "day" --length -1 --coin_pool 15000 --fetch_limit 1500 --no_cache
```

参数说明：
- `--config`: 配置文件路径，默认为`DataModule/data_config.yaml`
- `--workers`: 线程池工作线程数
- `--max_retries`: 最大重试次数
- `--output_path`: 输出目录路径
- `--symbol_list`: 符号列表文件路径，用于过滤币种
- `--vs_currency`: 计价货币，默认为"usd"
- `--interval`: 时间间隔，可选值为"minute"、"hour"、"day"
- `--length`: 数据长度，-1表示全部
- `--coin_pool`: 要获取的最大币种数量
- `--fetch_limit`: 每次API请求获取的币种数量，最大为1500
- `--no_cache`: 不使用缓存，强制从API获取数据

## 添加新的数据源

1. 在`Collectors`目录下创建新的收集器类，继承`BaseCollector`
2. 在`data_config.yaml`中添加新的数据源配置
3. 启动程序，框架会自动注册并使用新的收集器

## 配置文件示例

```yaml
DataSource:
  - Name: TokenInsight
    APIKey: ${TOKEN_INSIGHT_API_KEY}
    APILimit: ${TOKEN_INSIGHT_API_LIMIT}
    Collectors:
      - TokenInsightCoinListCollector
      - TokenInsightCoinHistoryCollector

Settings:
  Workers: 12
  MaxRetries: 3
  CachePath:
    CoinList: "DataModule/Cache/collected_coins.parquet"
    IntervalData: "DataModule/Cache/collected_coins_interval.parquet"
  OutputPath: "DataModule/Cache/coins_history"
  SymbolListPath: "symbol_list.csv"
  VSCurrency: "usd"
  Interval: "day"
  Length: -1
  CoinPool: 15000
  FetchLimit: 1500
  UseCache: true
``` 