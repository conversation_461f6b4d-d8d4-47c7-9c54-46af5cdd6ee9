import json
# from tornado.tcpserver import <PERSON><PERSON><PERSON><PERSON><PERSON>
# from tornado.ioloop import <PERSON><PERSON><PERSON>
from tornado.httpclient import AsyncHTTP<PERSON>lient
from tornado.web import Application, RequestHandler

_http_client = None

def get_http_client():
    global _http_client
    if _http_client is None:
        # set up connection pool
        AsyncHTTPClient.configure(
            "tornado.curl_httpclient.CurlAsyncHTTPClient",
            max_clients=100
        )
        _http_client = AsyncHTTPClient()
    return _http_client


async def async_get_request(url, connect_timeout=15, request_timeout=15, local_ip=None, **kwargs):
    client = get_http_client()
    headers = kwargs.get("headers", {})
    if "Connection" not in headers:
        headers["Connection"] = "keep-alive"  # plz keep alive
    
    response = await client.fetch(
        url,
        raise_error=False,
        method="GET",
        headers=headers,
        connect_timeout=connect_timeout,
        request_timeout=request_timeout,
        network_interface=local_ip
    )
    return response


async def async_post_request(
    url,
    data=None,
    proxy=False,
    connect_timeout=15,
    request_timeout=15,
    validate_cert=True,
    body=None,
    local_ip=None,
    **kwargs
):
    client = get_http_client()
    headers = {}
    if not kwargs.get("headers", {}):
        headers = {"Content-Type": "application/json"}
    headers.update(kwargs.get("headers", {}))
    if "Connection" not in headers:
        headers["Connection"] = "keep-alive"  # plz keep alive

    proxies = {"proxy_host": "http://127.0.0.1", "proxy_port": 7897} # use local proxy post

    _args = dict(
        method="POST",
        headers=headers,
        connect_timeout=connect_timeout,
        request_timeout=request_timeout,
        raise_error=False,
        allow_nonstandard_methods=True,
        network_interface=local_ip
    )

    if headers.get("Content-Type") == "application/x-www-form-urlencoded":
        from urllib.parse import urlencode

        if isinstance(data, dict):
            _args["body"] = urlencode(data)
        elif isinstance(data, str):
            _args["body"] = data
    else:
        if data:
            _args["body"] = json.dumps(data)
        if body:
            _args["body"] = body
    if proxy:
        _args.update(proxies)

    response = await client.fetch(url, **_args, validate_cert=validate_cert)
    return response



class BaseHTTPApplication(Application):
    def __init__(self, handlers):
        Application.__init__(self, handlers, debug=False, xsrf_cookies=False)
def gen_base_http_app(handlers):
    return BaseHTTPApplication(handlers)



class HTTPBaseHandler(RequestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header(
            "Access-Control-Allow-Headers", "Content-Type, x-requested-with"
        )
        self.set_header("Access-Control-Allow-Methods", "POST, GET, OPTIONS")

    def json_response(self, data):
        try:
            self.write(json.dumps(data))
        except:
            # maybe loghere
            pass

    def send_data(self, code:int=0, msg:str="", data=None):
        self.json_response({
            "code": code,
            "msg": msg,
            "data": data if data else {}
        })
    
    def send_fail(self, code:int=4000, msg:str="", data=None):
        self.json_response({
            "code": code,
            "msg": msg,
            "data": data if data else {}
        })

