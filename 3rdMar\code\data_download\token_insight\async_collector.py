"""
异步加密货币数据收集器

基于原有的 coin_mkc_collector.py 重构而成的异步版本，主要改进包括：
1. 使用 asyncio 替代 ThreadPoolExecutor 进行并发处理
2. 集成了独立的重试队列模块 (retry_queue.py)
3. 改进的API频率限制控制
4. 更好的错误处理和日志记录
5. 保持与原版本相同的业务逻辑和数据输出格式

使用方法:
python async_collector.py --concurrency 5 --coin_pool 1000 --max_retries 3

主要参数:
--concurrency: 并发请求数量，默认5
--coin_pool: 要获取的最大币种数量，默认15704
--max_retries: 最大重试次数，默认3
--rate_limit: API请求频率限制(每分钟)，默认使用环境变量
"""

import asyncio
import os
import time
import argparse
import polars as pl
from typing import Dict, Tuple, Optional
from collections import deque
import logging

# 导入现有模块
from coin_list_fetcher import fetch_coin_list, filter_by_symbol_list, save_to_parquet
from coin_history_fetcher import fetch_coin_history, save_to_parquet as save_history_to_parquet
from env_loader import TOKEN_INSIGHT_API_LIMIT
from retry_queue import RetryQueue

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("async_coin_collector.log")
    ]
)
logger = logging.getLogger(__name__)


class AsyncCoinDataCollector:
    """
    异步加密货币数据收集器
    使用asyncio进行并发网络请求，获取加密货币的历史数据
    """

    def __init__(self,
                 concurrency: int = 5,
                 coins_cache_path: str = "collected_coins.parquet",
                 symbol_list_path: str = "symbol_list.csv",
                 interval_output_path: str = "collected_coins_interval.parquet",
                 output_path: str = "./coins_mkc_history",
                 coin_pool: int = 15704,
                 fetch_limit: int = 1500,
                 vs_currency: str = "usd",
                 max_retries: int = 3,
                 rate_limit: int = None):
        """
        初始化异步收集器

        参数:
        concurrency (int): 并发请求数量，默认5
        coins_cache_path (str): 币种缓存文件路径
        symbol_list_path (str): 符号列表文件路径
        interval_output_path (str): 间隔数据输出文件路径
        output_path (str): 输出数据存放目录
        coin_pool (int): 要获取的最大币种数量，默认15704
        fetch_limit (int): 每次API请求获取的币种数量，最大1500
        vs_currency (str): 计价货币，默认为usd
        max_retries (int): 最大重试次数，默认3
        rate_limit (int): API请求频率限制(每分钟)，默认使用环境变量
        """
        self.concurrency = concurrency
        self.coins_cache_path = coins_cache_path
        self.symbol_list_path = symbol_list_path
        self.interval_output_path = interval_output_path
        self.output_path = output_path + "_" + time.strftime("%Y%m%d_%H%M%S")
        self.coin_pool = min(coin_pool, 16000)  # 最大支持约16000个币种
        self.fetch_limit = min(fetch_limit, 1500)  # 单次请求最大1500个币种
        self.vs_currency = vs_currency
        self.max_retries = max_retries
        self.rate_limit = rate_limit or TOKEN_INSIGHT_API_LIMIT

        # 创建输出目录（如果不存在）
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path, exist_ok=True)

        # 并发控制
        self.semaphore = asyncio.Semaphore(concurrency)
        self.rate_limit_semaphore = asyncio.Semaphore(self.rate_limit)

        # API请求频率控制
        self.request_times = deque(maxlen=self.rate_limit)

        # 币种时间间隔数据收集
        self.interval_data = []
        self.interval_lock = asyncio.Lock()

        # 初始化重试队列
        self.retry_queue = RetryQueue(
            max_retries=max_retries,
            retry_delay=0.5,
            max_concurrent_retries=min(concurrency, 3),
            enable_exponential_backoff=True
        )

        # 统计信息
        self.successful_count = 0
        self.failed_count = 0
        self.stats_lock = asyncio.Lock()

    async def wait_for_rate_limit(self):
        """
        等待API请求频率限制
        确保请求不超过每分钟的限制
        """
        now = time.time()

        # 清理一分钟前的请求记录
        while self.request_times and now - self.request_times[0] >= 60:
            self.request_times.popleft()

        # 如果队列已满，等待最早的请求过期
        if len(self.request_times) >= self.rate_limit:
            wait_time = 60 - (now - self.request_times[0])
            if wait_time > 0:
                logger.debug(f"API请求频率限制，等待 {wait_time:.2f} 秒")
                await asyncio.sleep(wait_time)
                # 重新清理过期记录
                now = time.time()
                while self.request_times and now - self.request_times[0] >= 60:
                    self.request_times.popleft()

        # 记录当前请求时间
        self.request_times.append(time.time())

    async def get_coin_list(self) -> Optional[pl.DataFrame]:
        """
        异步获取符合条件的币种列表

        返回:
        Optional[pl.DataFrame]: 过滤后的币种列表
        """
        # 如果本地已缓存，直接读取并过滤
        if os.path.exists(self.coins_cache_path):
            logger.info(f"使用本地缓存的币种列表: {self.coins_cache_path}")
            df = pl.read_parquet(self.coins_cache_path)
            # 对缓存的数据也进行过滤
            return filter_by_symbol_list(df, self.symbol_list_path)

        # 否则，分批次获取币种列表
        logger.info(f"开始获取币种列表，目标数量: {self.coin_pool}")

        all_coins_df = None
        offset = 0
        batches_count = 0

        # 计算需要获取的批次数
        total_batches = (self.coin_pool + self.fetch_limit - 1) // self.fetch_limit

        # 收集失败的批次，用于重试
        failed_batches = []

        while offset < self.coin_pool:
            batches_count += 1
            logger.info(f"获取币种批次 {batches_count}/{total_batches}，偏移量: {offset}")

            # 等待API请求频率限制
            await self.wait_for_rate_limit()

            # 计算当前批次应获取的数量
            current_limit = min(self.fetch_limit, self.coin_pool - offset)

            try:
                # 获取当前批次的币种列表
                batch_df = await fetch_coin_list(
                    limit=current_limit,
                    offset=offset,
                    vs_currency=self.vs_currency
                )

                if batch_df is None or batch_df.is_empty():
                    logger.warning(f"获取币种批次 {batches_count} 失败或返回空列表，将在后续重试")
                    failed_batches.append((current_limit, offset, batches_count))
                    offset += self.fetch_limit
                    continue

                logger.info(f"批次 {batches_count} 获取到 {len(batch_df)} 个币种")

                # 合并数据
                if all_coins_df is None:
                    all_coins_df = batch_df
                else:
                    all_coins_df = pl.concat([all_coins_df, batch_df])

                # 如果返回的数据少于请求的数量，说明已经获取完所有可用的币种
                if len(batch_df) < current_limit:
                    logger.info(f"已获取所有可用币种，总数: {len(all_coins_df)}")
                    break

            except Exception as e:
                logger.error(f"获取币种批次 {batches_count} 时出错: {e}")
                failed_batches.append((current_limit, offset, batches_count))

            # 更新偏移量
            offset += self.fetch_limit

        # 处理失败的批次重试
        if failed_batches:
            logger.info(f"开始重试 {len(failed_batches)} 个失败的批次...")
            for limit, offset, batch_num in failed_batches:
                try:
                    await self.wait_for_rate_limit()
                    batch_df = await fetch_coin_list(
                        limit=limit,
                        offset=offset,
                        vs_currency=self.vs_currency
                    )
                    if batch_df is not None and not batch_df.is_empty():
                        if all_coins_df is None:
                            all_coins_df = batch_df
                        else:
                            all_coins_df = pl.concat([all_coins_df, batch_df])
                        logger.info(f"重试批次 {batch_num} 成功，获取到 {len(batch_df)} 个币种")
                    else:
                        logger.error(f"重试批次 {batch_num} 仍然失败")
                except Exception as e:
                    logger.error(f"重试批次 {batch_num} 时出错: {e}")

        if all_coins_df is not None and not all_coins_df.is_empty():
            logger.info(f"共获取 {len(all_coins_df)} 个币种")

            # 过滤并保存
            filtered_df = filter_by_symbol_list(all_coins_df, self.symbol_list_path)
            save_to_parquet(filtered_df, self.coins_cache_path)
            return filtered_df

        logger.error("获取币种列表失败")
        return None

    async def fetch_coin_history_task(self, coin_id: str, symbol: str) -> Tuple[bool, Dict]:
        """
        获取币种历史数据的任务函数（不包含重试逻辑）

        参数:
        coin_id (str): 币种ID
        symbol (str): 币种符号

        返回:
        Tuple[bool, Dict]: (是否成功, 币种数据)
        """
        try:
            # 等待API请求频率限制
            await self.wait_for_rate_limit()

            logger.info(f"正在获取 {symbol}({coin_id}) 的历史数据...")

            # 调用现有模块获取历史数据
            df, symbol_returned, first_timestamp, last_timestamp, current_market_cap = await fetch_coin_history(
                coin_id, interval="day", length=-1, vs_currency=self.vs_currency
            )

            if df is None or df.is_empty():
                logger.warning(f"{symbol}({coin_id}) 没有历史数据或获取失败")
                return False, {"coin_id": coin_id, "symbol": symbol}

            # 保存历史数据
            save_history_to_parquet(df, symbol_returned, coin_id, first_timestamp, last_timestamp, self.output_path)

            # 线程安全地记录时间戳信息
            async with self.interval_lock:
                self.interval_data.append({
                    "symbol": symbol_returned,
                    "id": coin_id,
                    "start_timestamp": first_timestamp,
                    "end_timestamp": last_timestamp,
                    "current_market_cap": current_market_cap
                })

            # 更新统计信息
            async with self.stats_lock:
                self.successful_count += 1

            logger.info(f"成功获取 {symbol}({coin_id}) 的历史数据")
            return True, {
                "coin_id": coin_id,
                "symbol": symbol_returned,
                "first_timestamp": first_timestamp,
                "last_timestamp": last_timestamp,
                "current_market_cap": current_market_cap
            }

        except Exception as e:
            logger.error(f"获取 {symbol}({coin_id}) 历史数据时出错: {e}")
            async with self.stats_lock:
                self.failed_count += 1
            return False, {"coin_id": coin_id, "symbol": symbol}

    async def fetch_coin_history_with_retry(self, coin_id: str, symbol: str) -> Tuple[bool, Dict]:
        """
        获取币种历史数据，支持重试（包装函数）

        参数:
        coin_id (str): 币种ID
        symbol (str): 币种符号

        返回:
        Tuple[bool, Dict]: (是否成功, 币种数据)
        """
        async with self.semaphore:
            # 首次尝试
            success, result = await self.fetch_coin_history_task(coin_id, symbol)

            # 如果失败，添加到重试队列
            if not success:
                await self.retry_queue.add_task(
                    task_func=self.fetch_coin_history_task,
                    task_args=(coin_id, symbol),
                    task_id=f"{symbol}_{coin_id}",
                    metadata={"coin_id": coin_id, "symbol": symbol}
                )

            return success, result

    async def save_interval_data(self):
        """
        保存币种时间间隔数据
        """
        if not self.interval_data:
            logger.info("没有间隔数据可保存")
            return

        try:
            df = pl.DataFrame(self.interval_data)
            full_interval_path = os.path.join(self.output_path, self.interval_output_path)
            df.write_parquet(full_interval_path)
            logger.info(f"币种时间间隔数据已保存到 {full_interval_path}")
            logger.info(f"收集了 {len(self.interval_data)} 个币种的时间间隔数据")
        except Exception as e:
            logger.error(f"保存间隔数据时出错: {e}")

    async def run(self):
        """
        运行主程序
        """
        start_time = time.time()

        # 获取币种列表
        coins_df = await self.get_coin_list()

        if coins_df is None or coins_df.is_empty():
            logger.error("未获取到符合条件的币种列表")
            return

        logger.info(f"获取到 {len(coins_df)} 个符合条件的币种")

        # 提取币种ID和符号
        coins_data = coins_df.select(["id", "symbol"]).to_dicts()

        # 启动后台重试队列处理器
        retry_processor_task = await self.retry_queue.start_background_processor()

        try:
            # 创建所有任务
            tasks = [
                self.fetch_coin_history_with_retry(coin["id"], coin["symbol"])
                for coin in coins_data
            ]

            logger.info(f"开始并发获取 {len(tasks)} 个币种的历史数据，并发数: {self.concurrency}")

            # 并发执行所有任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            successful = 0
            failed = 0

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"任务 {i} 执行时出现异常: {result}")
                    failed += 1
                else:
                    success, _ = result
                    if success:
                        successful += 1
                    else:
                        failed += 1

            # 等待重试队列处理完成
            logger.info("等待重试队列处理完成...")
            await self.retry_queue.wait_for_completion()

            # 获取重试统计信息
            retry_stats = self.retry_queue.get_stats()

            end_time = time.time()

            # 保存时间间隔数据
            await self.save_interval_data()

            # 输出统计信息
            logger.info("=" * 50)
            logger.info("任务完成统计:")
            logger.info(f"总币种数: {len(coins_data)}")
            logger.info(f"初次成功: {successful}")
            logger.info(f"初次失败: {failed}")
            logger.info(f"重试成功: {retry_stats['successful_retries']}")
            logger.info(f"重试失败: {retry_stats['failed_retries']}")
            logger.info(f"最终成功: {self.successful_count}")
            logger.info(f"最终失败: {self.failed_count}")
            logger.info(f"总耗时: {end_time - start_time:.2f} 秒")
            logger.info("=" * 50)

        finally:
            # 停止后台重试队列处理器
            retry_processor_task.cancel()
            try:
                await retry_processor_task
            except asyncio.CancelledError:
                pass


def main():
    """
    主函数，解析命令行参数并运行收集器
    """
    parser = argparse.ArgumentParser(description="异步加密货币数据收集器")
    parser.add_argument("--concurrency", type=int, default=5, help="并发请求数量，默认5")
    parser.add_argument("--coins_cache", type=str, default="collected_coins.parquet", help="币种缓存文件路径")
    parser.add_argument("--symbol_list", type=str, default="symbol_list.csv", help="符号列表文件路径")
    parser.add_argument("--interval_output", type=str, default="collected_coins_interval.parquet",
                        help="间隔数据输出文件路径")
    parser.add_argument("--output_path", type=str, default="./coins_mkc_history", help="输出数据存放目录")
    parser.add_argument("--coin_pool", type=int, default=15704, help="要获取的最大币种数量，最大约15704")
    parser.add_argument("--fetch_limit", type=int, default=1500, help="每次API请求获取的币种数量，最大1500")
    parser.add_argument("--vs_currency", type=str, default="usd", help="计价货币，默认为usd")
    parser.add_argument("--max_retries", type=int, default=3, help="最大重试次数，默认3")
    parser.add_argument("--rate_limit", type=int, default=None, help="API请求频率限制(每分钟)，默认使用环境变量")

    args = parser.parse_args()

    collector = AsyncCoinDataCollector(
        concurrency=args.concurrency,
        coins_cache_path=args.coins_cache,
        symbol_list_path=args.symbol_list,
        interval_output_path=args.interval_output,
        output_path=args.output_path,
        coin_pool=args.coin_pool,
        fetch_limit=args.fetch_limit,
        vs_currency=args.vs_currency,
        max_retries=args.max_retries,
        rate_limit=args.rate_limit
    )

    # 运行异步收集器
    asyncio.run(collector.run())


if __name__ == "__main__":
    main()
