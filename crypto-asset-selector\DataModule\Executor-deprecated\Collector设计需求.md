## 一、任务概要

现在我需要设计一个Collector来收集来自不同的数据源API的数据，请你参考coin_mkc_collector的结构，设计一个collector框架。

## 二、具体要求

### 1. Collector

请参考coin_mkc_collector.py的写法，将其抽象成一个框架，使得来自不同的数据源的API都可以使用这个collector的业务逻辑进行数据采集。

Collector使用模板模式，可参考以下代码：

```python
class BaseCollector:
    """API调用模板"""
    def run(self, params):
        self.pre_handle(params)
        result = self.handle(params)
        return self.post_handle(result)
        
    def pre_handle(self, params):
        # 默认实现或钩子
        pass
        
    def handle(self, params):
        # 具体实现由子类提供
        pass
        
    def post_handle(self, result):
        # 默认实现或钩子
        return result
```

同时使用策略模式，支持不同类型的请求策略，可参考以下代码：

```python
class ParallelCollector(BaseCollector):
    
    def run(self, params):
        pass

class SerialCollector(BaseCollector):

    def run(self, params):
        pass

```


### 2. StrategyFactory

StrategyFactory用于动态解析配置，使用工厂模式，指定各数据源API使用哪种Collector进行数据采集，可参考以下代码：

```python
class StrategyFactory:
    """连接工厂"""
    @staticmethod
    def create_collector(self, config):
        if config["strategy"] == "parallel":
            return ParallelCollector(config)
        elif config["strategy"] == "serial":
            return SerialCollector(config)
        # 其他类型...
```
### 3. Config
