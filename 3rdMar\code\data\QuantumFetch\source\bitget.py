from data.QuantumFetch.common.base import *
from data.QuantumFetch.common.utils import *

async def demo():
    res = await async_get_request("https://api.bitget.com/api/spot/v1/public/currencies")
    print(res.code)
    print(res.reason)
    # bot = get_bot("feishu", {"ENV": "TEST", "source": "BITGET"})
    # await bot.async_send(f"res: {str(res.code)} {str(res.reason)} {int(time.time())}")
    # print(res.body) # TODO: you should process this