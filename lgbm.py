import numpy as np
import pandas as pd
import polars as pl
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import lightgbm as lgb
from datetime import datetime
import joblib

class PriceTrendPredictor:
    def __init__(self, window_size=10, trend_threshold=0.001, predict_periods=1):
        """
        初始化价格趋势预测器
        
        参数:
        window_size (int): 滑动窗口大小，表示使用多少个历史数据点来预测
        trend_threshold (float): 价格变化的阈值，用于定义上涨和下跌趋势
        predict_periods (int): 预测未来多少个时间段的趋势
        """
        self.window_size = window_size
        self.trend_threshold = trend_threshold
        self.predict_periods = predict_periods
        self.model = None
        self.feature_names = None
        
    def _create_features(self, df):
        """
        从原始数据创建特征
        
        参数:
        df (polars.DataFrame): 原始K线数据
        
        返回:
        polars.DataFrame: 包含特征的DataFrame
        """
        # 转换为pandas以方便处理
        if isinstance(df, pl.DataFrame):
            df = df.to_pandas()
            
        # 计算价格变化
        df['price_change'] = df['close'].pct_change()
        df['price_change_prev'] = df['price_change'].shift(1)
        
        # 计算移动平均线
        df['ma5'] = df['close'].rolling(window=5).mean()
        df['ma10'] = df['close'].rolling(window=10).mean()
        df['ma20'] = df['close'].rolling(window=20).mean()
        
        # 计算技术指标
        # MACD
        df['ema12'] = df['close'].ewm(span=12, adjust=False).mean()
        df['ema26'] = df['close'].ewm(span=26, adjust=False).mean()
        df['macd'] = df['ema12'] - df['ema26']
        df['signal'] = df['macd'].ewm(span=9, adjust=False).mean()
        df['macd_hist'] = df['macd'] - df['signal']
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量特征
        df['volume_change'] = df['volume'].pct_change()
        df['volume_ma5'] = df['volume'].rolling(window=5).mean()
        
        # 波动率
        df['volatility'] = df['close'].rolling(window=10).std()
        
        # 价格与移动平均线的差距
        df['close_ma5_diff'] = df['close'] - df['ma5']
        df['close_ma10_diff'] = df['close'] - df['ma10']
        
        # 高低价差距
        df['hl_diff'] = df['high'] - df['low']
        df['hl_pct'] = df['hl_diff'] / df['low']
        
        # 去除NaN值
        df = df.dropna()
        
        return df
    
    def _create_labels(self, df, periods=1):
        """
        创建标签：未来价格趋势（上涨=1，下跌=0）
        
        参数:
        df (pandas.DataFrame): 包含价格数据的DataFrame
        periods (int): 预测未来多少个时间段的趋势
        
        返回:
        pandas.DataFrame: 包含标签的DataFrame
        """
        # 计算未来价格变化
        future_change = df['close'].shift(-periods) / df['close'] - 1
        
        # 根据价格变化和阈值来定义趋势
        df['trend'] = (future_change > self.trend_threshold).astype(int)
        
        return df
    
    def _prepare_time_series_data(self, df):
        """
        准备时间序列数据，创建滑动窗口特征
        
        参数:
        df (pandas.DataFrame): 包含特征和标签的DataFrame
        
        返回:
        tuple: (X, y) 特征矩阵和标签向量
        """
        # 选择特征列（排除日期和标签列）
        feature_cols = [col for col in df.columns if col not in ['candle_begin_time_GMT8', 'trend']]
        self.feature_names = feature_cols
        
        X_list = []
        y_list = []
        
        # 创建滑动窗口数据
        for i in range(len(df) - self.window_size - self.predict_periods + 1):
            X_window = df[feature_cols].iloc[i:i+self.window_size].values.flatten()
            y_value = df['trend'].iloc[i+self.window_size-1]
            
            X_list.append(X_window)
            y_list.append(y_value)
        
        return np.array(X_list), np.array(y_list)
    
    def train(self, df, test_size=0.2, random_state=42):
        """
        训练模型
        
        参数:
        df (polars.DataFrame or pandas.DataFrame): 原始K线数据
        test_size (float): 测试集比例
        random_state (int): 随机种子
        
        返回:
        dict: 包含训练和验证指标的字典
        """
        # 特征工程
        df_features = self._create_features(df)
        
        # 创建标签
        df_labeled = self._create_labels(df_features, self.predict_periods)
        
        # 准备时间序列数据
        X, y = self._prepare_time_series_data(df_labeled)
        
        # 训练测试集分割
        # 注意：对于时间序列数据，最好使用时间顺序分割而不是随机分割
        train_size = int(len(X) * (1 - test_size))
        X_train, X_test = X[:train_size], X[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        # 创建LightGBM数据集
        train_data = lgb.Dataset(X_train, label=y_train)
        valid_data = lgb.Dataset(X_test, label=y_test, reference=train_data)
        
        # 设置LightGBM参数
        params = {
            'boosting_type': 'gbdt',
            'objective': 'binary',
            'metric': ['binary_logloss', 'auc'],
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1
        }
        
        # 训练模型
        self.model = lgb.train(
            params,
            train_data,
            num_boost_round=1000,
            valid_sets=[train_data, valid_data],
            callbacks=[lgb.early_stopping(stopping_rounds=50)],
        )
        
        # 预测
        y_pred_proba = self.model.predict(X_test)
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        # 评估模型
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred, zero_division=0)
        f1 = f1_score(y_test, y_pred, zero_division=0)
        cm = confusion_matrix(y_test, y_pred)
        
        # 特征重要性
        feature_imp = pd.DataFrame({
            'Feature': [f'Feature_{i}' for i in range(len(X_train[0]))],
            'Importance': self.model.feature_importance()
        }).sort_values(by='Importance', ascending=False)
        
        # 返回评估结果
        evaluation = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'confusion_matrix': cm,
            'feature_importance': feature_imp
        }
        
        return evaluation
    
    def predict(self, df):
        """
        使用训练好的模型进行预测
        
        参数:
        df (polars.DataFrame or pandas.DataFrame): 原始K线数据
        
        返回:
        numpy.ndarray: 预测的趋势（上涨=1，下跌=0）
        """
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用train方法")
        
        # 特征工程
        df_features = self._create_features(df)
        
        # 准备预测数据（不包括标签）
        X_list = []
        timestamps = []
        
        # 获取特征列
        feature_cols = self.feature_names
        
        # 创建滑动窗口数据用于预测
        for i in range(len(df_features) - self.window_size + 1):
            X_window = df_features[feature_cols].iloc[i:i+self.window_size].values.flatten()
            timestamp = df_features.iloc[i+self.window_size-1]['candle_begin_time_GMT8']
            
            X_list.append(X_window)
            timestamps.append(timestamp)
        
        X = np.array(X_list)
        
        # 预测
        y_pred_proba = self.model.predict(X)
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        # 创建结果DataFrame
        results = pd.DataFrame({
            'timestamp': timestamps,
            'trend_probability': y_pred_proba,
            'predicted_trend': y_pred
        })
        
        return results
    
    def save_model(self, filepath):
        """保存模型到文件"""
        if self.model is None:
            raise ValueError("模型尚未训练，无法保存")
        
        model_data = {
            'model': self.model,
            'window_size': self.window_size,
            'trend_threshold': self.trend_threshold,
            'predict_periods': self.predict_periods,
            'feature_names': self.feature_names
        }
        
        joblib.dump(model_data, filepath)
        print(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath):
        """从文件加载模型"""
        model_data = joblib.load(filepath)
        
        self.model = model_data['model']
        self.window_size = model_data['window_size']
        self.trend_threshold = model_data['trend_threshold']
        self.predict_periods = model_data['predict_periods']
        self.feature_names = model_data['feature_names']
        
        print(f"模型已从 {filepath} 加载")

    def visualize_results(self, df, predictions):
        """
        可视化预测结果
        
        参数:
        df (pandas.DataFrame): 原始数据
        predictions (pandas.DataFrame): 预测结果
        """
        # 合并原始数据和预测
        if isinstance(df, pl.DataFrame):
            df = df.to_pandas()
            
        merged = pd.merge(
            df, 
            predictions,
            left_on='candle_begin_time_GMT8',
            right_on='timestamp',
            how='inner'
        )
        
        # 绘制价格和预测趋势
        plt.figure(figsize=(14, 8))
        
        # 价格图
        ax1 = plt.subplot(2, 1, 1)
        ax1.plot(merged['candle_begin_time_GMT8'], merged['close'], label='收盘价')
        
        # 标记预测的上涨和下跌点
        up_indices = merged[merged['predicted_trend'] == 1].index
        down_indices = merged[merged['predicted_trend'] == 0].index
        
        ax1.scatter(
            merged.loc[up_indices, 'candle_begin_time_GMT8'],
            merged.loc[up_indices, 'close'],
            color='green', marker='^', s=50, label='预测上涨'
        )
        ax1.scatter(
            merged.loc[down_indices, 'candle_begin_time_GMT8'],
            merged.loc[down_indices, 'close'],
            color='red', marker='v', s=50, label='预测下跌'
        )
        
        ax1.set_title('价格和预测趋势')
        ax1.set_ylabel('价格')
        ax1.grid(True)
        ax1.legend()
        
        # 预测概率图
        ax2 = plt.subplot(2, 1, 2, sharex=ax1)
        ax2.plot(merged['candle_begin_time_GMT8'], merged['trend_probability'], 'b-', label='上涨概率')
        ax2.axhline(y=0.5, color='r', linestyle='--', label='阈值 (0.5)')
        ax2.set_ylim(0, 1)
        ax2.set_title('预测上涨概率')
        ax2.set_ylabel('概率')
        ax2.set_xlabel('时间')
        ax2.grid(True)
        ax2.legend()
        
        plt.tight_layout()
        plt.show()


def load_and_preprocess_data(file_path, date_format='%Y-%m-%d %H:%M:%S.%f'):
    """
    加载并预处理数据
    
    参数:
    file_path (str): 数据文件路径
    date_format (str): 日期格式
    
    返回:
    polars.DataFrame: 预处理后的数据
    """
    # 使用polars加载数据
    df = pl.read_csv(file_path)
    
    # 确保日期列是字符串类型
    df = df.with_column(
        pl.col('candle_begin_time_GMT8').cast(pl.Utf8)
    )
    
    # 确保日期列是datetime类型
    df = df.with_column(
        pl.col('candle_begin_time_GMT8').str.to_datetime(format=date_format)
    )
    
    # 按时间排序
    df = df.sort('candle_begin_time_GMT8')
    
    return df


def main():
    """主函数，演示模型训练和预测流程"""
    
    # 请替换为实际数据文件路径
    file_path = 'price_data.csv'
    
    try:
        # 加载数据
        df = load_and_preprocess_data(file_path)
        print(f"数据加载成功，共 {len(df)} 条记录")
        
        # 分割为训练集和测试集（保持时间顺序）
        train_size = int(len(df) * 0.8)
        train_data = df.slice(0, train_size)
        test_data = df.slice(train_size, len(df))
        
        print(f"训练集大小: {len(train_data)}, 测试集大小: {len(test_data)}")
        
        # 初始化模型
        model = PriceTrendPredictor(
            window_size=10,  # 使用10个时间点的数据进行预测
            trend_threshold=0.001,  # 价格变化0.1%以上视为趋势变化
            predict_periods=1  # 预测下一个时间点的趋势
        )
        
        # 训练模型
        print("开始训练模型...")
        evaluation = model.train(train_data)
        
        # 打印评估结果
        print("\n模型评估结果:")
        print(f"准确率: {evaluation['accuracy']:.4f}")
        print(f"精确率: {evaluation['precision']:.4f}")
        print(f"召回率: {evaluation['recall']:.4f}")
        print(f"F1 分数: {evaluation['f1_score']:.4f}")
        print("\n混淆矩阵:")
        print(evaluation['confusion_matrix'])
        
        # 在测试集上进行预测
        print("\n在测试集上进行预测...")
        predictions = model.predict(test_data)
        
        # 可视化结果
        print("\n可视化预测结果...")
        model.visualize_results(test_data, predictions)
        
        # 保存模型
        model.save_model('price_trend_model.joblib')
        
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main() 