# 导入函数库
from jqdata import *
from datetime import datetime, timedelta
import pandas as pd
import math
from sortedcontainers import SortedDict
import numpy as np
import talib
import json

class Node:
    """节点类，用于存储日期和对应的指标值"""
    def __init__(self, date, val):
        self.date = date
        self.val = val

    def __repr__(self):
        return f"Node(date={self.date}, val={self.val})"

class QuantileCalculator:
    """分位值计算器（原Zset优化版）"""
    def __init__(self, series, percentile_low=0.3, percentile_high=0.7):
        self.nodes = SortedDict()
        self.val_nodes_length = SortedDict()
        self.total_nodes = 0
        self.date_index = {}
        self.percentile_low = percentile_low
        self.percentile_high = percentile_high
        
        for date, val in series.items():
            self._insert(Node(date, val))

    def _update_val_nodes_length(self, val, delta):
        self.val_nodes_length[val] = self.val_nodes_length.get(val, 0) + delta
        self.total_nodes += delta

    def _insert(self, node):
        if node.val not in self.nodes:
            self.nodes[node.val] = []
        self.nodes[node.val].append(node)
        self.date_index[node.date] = node
        self._update_val_nodes_length(node.val, 1)

    def update_series(self, new_series):
        """更新数据序列"""
        for date, val in new_series.items():
            if date not in self.date_index:
                self._insert(Node(date, val))

    def delete(self, date):
        """删除指定日期的数据"""
        if date in self.date_index:
            node = self.date_index[date]
            self.nodes[node.val].remove(node)
            self._update_val_nodes_length(node.val, -1)
            if not self.nodes[node.val]:
                del self.nodes[node.val]
                del self.val_nodes_length[node.val]
            del self.date_index[date]

    def get_quantiles(self):
        """获取低分位值和高分位值"""
        low_idx = int(self.percentile_low * self.total_nodes)
        high_idx = int((1 - self.percentile_high) * self.total_nodes)
        
        low_val = self._find_quantile(low_idx, reverse=False)
        high_val = self._find_quantile(high_idx, reverse=True)
        return low_val, high_val

    def _find_quantile(self, target_idx, reverse=False):
        """查找分位值"""
        cumulative = 0
        iterator = reversed(self.val_nodes_length.items()) if reverse else self.val_nodes_length.items()
        for val, count in iterator:
            cumulative += count
            if cumulative > target_idx:
                return val
        return 0

class PositionOptimizer:
    """仓位优化器（非线性计算模式）"""
    def __init__(self, config=None):
        if config is None:
            # 默认配置
            self.config = {
                'base_unit': 1.0,
                'max_ratio': 5.0,
                'min_ratio': 0.2,
                'risk_params': {
                    'pe_weight': 0.4,
                    'pb_weight': 0.4,
                    'rsi_weight': 0.1,
                    'macd_weight': 0.1
                }
            }
        else:
            self.config = config
        
        self.base_unit = self.config['base_unit']
        self.max_ratio = self.config['max_ratio']
        self.min_ratio = self.config['min_ratio']
        self.risk_params = self.config['risk_params']

    def calculate_signal(self, indicators, position_type):
        """生成非线性交易信号"""
        signal = 0
        
        # 核心估值信号
        pe_signal = self._exp_signal(indicators['pe_deviation'])
        pb_signal = self._exp_signal(indicators['pb_deviation'])
        signal += self.risk_params['pe_weight'] * pe_signal
        signal += self.risk_params['pb_weight'] * pb_signal

        # 技术指标增强
        if 'rsi' in indicators:
            rsi_factor = self._rsi_enhancement(indicators['rsi'])
            signal += self.risk_params['rsi_weight'] * rsi_factor
            
        if 'macd' in indicators:
            macd_factor = self._macd_enhancement(indicators['macd'])
            signal += self.risk_params['macd_weight'] * macd_factor

        # 波动率控制
        if 'volatility' in indicators:
            signal *= self._volatility_adjustment(indicators['volatility'])

        # 生成最终仓位
        ratio = self.min_ratio + (self.max_ratio - self.min_ratio) * (1 - math.exp(-signal))
        return ratio * self.base_unit * self._position_direction(position_type)

    def _exp_signal(self, deviation):
        """指数信号转换，扩大偏离度信号"""
        return math.exp(max(deviation, 0)) - 1 if deviation > 0 else 0

    def _rsi_enhancement(self, rsi):
        """RSI增强因子，过低买入过高卖出"""
        return (50 - rsi)/50 if rsi < 50 else (rsi - 50)/50 * -1

    def _macd_enhancement(self, macd):
        """MACD增强因子"""
        return macd if abs(macd) > 0.5 else 0

    def _volatility_adjustment(self, volatility):
        """波动率调整因子，高波动降低仓位"""
        return 1 / (1 + math.log1p(volatility))

    def _position_direction(self, position_type):
        """仓位方向，多空控制"""
        return 1 if position_type == 'long' else -1

def calculate_deviation(current_value, calculator):
    """计算当前值与分位值的偏差"""
    chance_val, danger_val = calculator.get_quantiles()
    if current_value <= chance_val:
        # 低于机会值，正偏差（买入信号）
        return (chance_val - current_value) / chance_val
    elif current_value >= danger_val:
        # 高于危险值，正偏差（卖出信号）
        return (current_value - danger_val) / danger_val
    return 0

def get_rsi(code='000300.XSHG', timeperiod=14):
    """获取RSI指标"""
    prices = get_price(code, count=timeperiod+10, frequency='daily', fields='close')
    rsi = talib.RSI(prices['close'].values, timeperiod=timeperiod)[-1]
    return rsi

def get_macd(code='000300.XSHG'):
    """获取MACD指标"""
    prices = get_price(code, count=40, frequency='daily', fields='close')
    close = prices['close'].values
    macd, signal, hist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
    return hist[-1]

def get_latest_valuation(current_dt):
    """获取最新估值数据"""
    pe_pb_ttm_today = cal_index_pe_pb('000300.XSHG', current_dt, current_dt)
    pe_pb_ttm_today = pe_pb_ttm_today[['pb_ttm_index', 'pe_ttm_index']]
    pb_val = pe_pb_ttm_today['pb_ttm_index'][0]
    pe_val = pe_pb_ttm_today['pe_ttm_index'][0]
    return pe_val, pb_val

def cal_index_pe_pb(code, start_date, end_date): 
    """计算指数的PE和PB值"""
    index_data = get_price(code, start_date=start_date, end_date=end_date, frequency='daily', fields=None, skip_paused=False, fq='pre', count=None, panel=False, fill_paused=True)
    time_list = index_data.index.tolist()
    df = get_valuation(code, start_date=None, end_date=end_date, fields=['pe_ratio','pb_ratio','market_cap'], count=1)
    for date in time_list:
        # 获取成份股
        stock_list = get_index_stocks(code, date=date)
        data = get_valuation(stock_list, start_date=None, end_date=date, fields=['pe_ratio','pb_ratio','market_cap'], count=1)
        df = pd.concat([df,data]).reset_index(drop=True)
    df = df.drop_duplicates()
    df['net_income_ttm'] = df['market_cap']/df['pe_ratio']
    df['book_value'] = df['market_cap']/df['pb_ratio']
    valuation = df.groupby('day').sum()
    valuation['pe_ttm_index'] = valuation['market_cap']/valuation['net_income_ttm']
    valuation['pb_ttm_index'] = valuation['market_cap']/valuation['book_value']
    return valuation

# 初始化函数
def initialize(context):
    # 设定基准
    set_benchmark('000300.XSHG')
    # 开启动态复权模式(真实价格)
    set_option("use_real_price", True)

    ### 场外基金相关设定 ###
    # 设置账户类型: 场外基金账户
    set_subportfolios([SubPortfolioConfig(context.portfolio.cash, 'open_fund')])
    # 设置赎回到账日
    set_redeem_latency(4, 'stock_fund')
    
    # 初始化历史数据
    cur_date = context.current_dt
    first_start_date = cur_date - timedelta(days=5 * 365)
    first_start_date = first_start_date.strftime('%Y-%m-%d')
    
    # 维护沪深300历史PE，PB数据
    pe_pb_que = cal_index_pe_pb('000300.XSHG', first_start_date, cur_date.strftime('%Y-%m-%d'))
    
    # 使用QuantileCalculator替代Zset
    g.pe_calculator = QuantileCalculator(pe_pb_que['pe_ttm_index'])
    g.pb_calculator = QuantileCalculator(pe_pb_que['pb_ttm_index'])
    
    # 当前估值指标
    g.pe_today = None
    g.pb_today = None
    
    # 仓位配置参数
    g.position_config = {
        'base_unit': 1.0,
        'max_ratio': 5.0,
        'min_ratio': 0.2,
        'risk_params': {
            'pe_weight': 0.4,
            'pb_weight': 0.4,
            'rsi_weight': 0.1,
            'macd_weight': 0.1
        }
    }
    
    # 设置资金单位
    allcash = context.portfolio.available_cash
    g.cash_unit = allcash / (5*12) # 5年窗口，以月为频率，平均分配

    # 基金代码
    g.fund_code = '519671.OF'
    
    # 创建仓位优化器
    g.position_optimizer = PositionOptimizer(g.position_config)
    
    # 运行函数
    run_daily(market_open, time='open', reference_security='000300.XSHG')
    run_daily(after_market_close, time='after_close', reference_security='000300.XSHG')

# 开盘时运行函数
def market_open(context):
    log.info("===================== 交易日开盘 =====================")
    
    # 获取当前日期
    today = context.current_dt
    today_str = today.strftime('%Y-%m-%d')
    
    # 获取最新估值数据
    g.pe_today, g.pb_today = get_latest_valuation(today)
    
    # 更新历史数据
    drop_date = (today - timedelta(days=5 * 365)).strftime('%Y-%m-%d')
    
    # 插入新数据并删除过期数据
    g.pe_calculator._insert(Node(today_str, g.pe_today))
    g.pe_calculator.delete(drop_date)
    
    g.pb_calculator._insert(Node(today_str, g.pb_today))
    g.pb_calculator.delete(drop_date)
    
    # 获取最新的机会值和危险值
    pe_chance, pe_danger = g.pe_calculator.get_quantiles()
    pb_chance, pb_danger = g.pb_calculator.get_quantiles()
    
    # 打印当前市场信息
    pe_pb_info = f"{today_str} PE: {g.pe_today:.2f} PB: {g.pb_today:.2f}"
    log.info(pe_pb_info)
    log.info(f"PE 机会值: {pe_chance:.2f} 危险值: {pe_danger:.2f}")
    log.info(f"PB 机会值: {pb_chance:.2f} 危险值: {pb_danger:.2f}")
    
    # 获取技术指标
    rsi = get_rsi()
    macd = get_macd()
    log.info(f"RSI: {rsi:.2f} MACD: {macd:.4f}")
    
    # 计算PE和PB偏离度
    pe_deviation = calculate_deviation(g.pe_today, g.pe_calculator)
    pb_deviation = calculate_deviation(g.pb_today, g.pb_calculator)
    
    # 买入信号时PE/PB偏离度为正，代表低于机会值
    if pe_deviation > 0 or pb_deviation > 0:
        # 生成交易信号
        indicators = {
            'pe_deviation': pe_deviation,
            'pb_deviation': pb_deviation,
            'rsi': rsi,
            'macd': macd
        }
        
        # 使用仓位优化器计算买入信号
        position_ratio = g.position_optimizer.calculate_signal(indicators, 'long')
        
        # 使用聚宽平台的purchase API进行买入
        if position_ratio > 0:
            add_cash = position_ratio * g.cash_unit
            log.info(f"****************BUY**********************")
            log.info(f"买入信号强度: {position_ratio:.2f}, 买入金额: {add_cash:.2f}")
            o = purchase(g.fund_code, add_cash)
            log.info(o)
            log.info(f"{today_str} PE: {g.pe_today:.2f} PB: {g.pb_today:.2f}")
            log.info(f"PE 机会值: {pe_chance:.2f} PB 机会值: {pb_chance:.2f}")

# 收盘后运行函数
def after_market_close(context):
    log.info("===================== 交易日收盘 =====================")
    
    # 获取当前信息
    today = context.current_dt
    today_str = today.strftime('%Y-%m-%d')
    
    # 打印当前市场信息
    pe_pb_info = f"{today_str} PE: {g.pe_today:.2f} PB: {g.pb_today:.2f}"
    log.info(pe_pb_info)
    
    # 获取最新的机会值和危险值
    pe_chance, pe_danger = g.pe_calculator.get_quantiles()
    pb_chance, pb_danger = g.pb_calculator.get_quantiles()
    
    log.info(f"PE 机会值: {pe_chance:.2f} 危险值: {pe_danger:.2f}")
    log.info(f"PB 机会值: {pb_chance:.2f} 危险值: {pb_danger:.2f}")
    
    # 获取技术指标
    rsi = get_rsi()
    macd = get_macd()
    log.info(f"RSI: {rsi:.2f} MACD: {macd:.4f}")
    
    # 计算PE和PB偏离度
    pe_deviation = calculate_deviation(g.pe_today, g.pe_calculator)
    pb_deviation = calculate_deviation(g.pb_today, g.pb_calculator)
    
    # 卖出信号时，PE/PB偏离度仍为正，但代表高于危险值
    if pe_deviation > 0 or pb_deviation > 0:
        # 生成交易信号
        indicators = {
            'pe_deviation': pe_deviation,
            'pb_deviation': pb_deviation,
            'rsi': rsi,
            'macd': macd
        }
        
        # 使用仓位优化器计算卖出信号（方向为短）
        position_ratio = g.position_optimizer.calculate_signal(indicators, 'short')
        
        # 使用聚宽平台的redeem API进行卖出
        if position_ratio < 0:
            cut_cash = abs(position_ratio) * g.cash_unit
            log.info(f"****************SELL*************************")
            log.info(f"卖出信号强度: {abs(position_ratio):.2f}, 卖出金额: {cut_cash:.2f}")
            o = redeem(g.fund_code, cut_cash)
            log.info(o)
            log.info(f"{today_str} PE: {g.pe_today:.2f} PB: {g.pb_today:.2f}")
            log.info(f"PE 危险值: {pe_danger:.2f} PB 危险值: {pb_danger:.2f}")
    
    # 查看账户信息
    p = context.portfolio.subportfolios[0]
    log.info('- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -')
    log.info('场外基金账户信息：')
    if g.fund_code in p.long_positions:
        log.info(f'基金持有份额：{p.long_positions[g.fund_code].closeable_amount}')
    else:
        log.info('当前未持有基金')
    log.info(f'可用现金：{p.available_cash:.2f}')
    log.info(f'总资产：{p.total_value:.2f}')
    log.info('##############################################################')