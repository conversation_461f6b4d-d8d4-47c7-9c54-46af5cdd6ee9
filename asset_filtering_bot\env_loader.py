import os
from dotenv import load_dotenv

# 加载.env文件中的环境变量
load_dotenv()

# API端点
TOKEN_INSIGHT_GET_COIN_LIST = os.getenv('TOKEN_INSIGHT_GET_COIN_LIST')
TOKEN_INSIGHT_GET_COIN_HISTORY = os.getenv('TOKEN_INSIGHT_GET_COIN_HISTORY')
TOKEN_INSIGHT_API_KEY = os.getenv('TOKEN_INSIGHT_API_KEY')
TOKEN_INSIGHT_API_LIMIT = int(os.getenv('TOKEN_INSIGHT_API_LIMIT', '60'))

def get_api_headers():
    """
    获取API请求需要的headers
    """
    return {
        'TI_API_KEY': TOKEN_INSIGHT_API_KEY,
        'accept': 'application/json'
    } 