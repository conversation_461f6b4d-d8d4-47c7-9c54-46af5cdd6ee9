DataSource:
  # - Name: CoinMarketCap
  #   APIKey: "sample_api_key"  # 示例API密钥
  #   APILimit: 30
  #   Collectors:
  #     - Name: CoinMarketCapCollector
  #       Params:
  #         output_path: "DataModule/Cache/cmc_data.parquet"

  # - Name: MockSource
  #   APIKey: "mock_api_key"
  #   APILimit: 100
  #   Collectors:
  #     - Name: MockCollector
  #       DependsOn: CoinMarketCapCollector
  #       Params:
  #         source_name: "Mock数据源"

  - Name: AlphaVantage
    APIKey: CL9PUUI1UG44QPE0
    APILimit: 5
    Collectors:
      - Name: AlphaVantageIntradayNasdaqCollector
        Params:
          function: TIME_SERIES_INTRADAY
          symbol: "QQQ"
          interval: "5min"
          extended_hours: "true"
          outputsize: "full"
          datatype: "csv"
          output_path: "DataModule/Cache/alvt_intraday_nasdaq.parquet"  

# 全局设置，可以被命令行参数覆盖
Settings:
  Workers: 12
  MaxRetries: 3
  CachePath:
    CoinList: "DataModule/Cache/collected_coins.parquet"
    IntervalData: "DataModule/Cache/collected_coins_interval.parquet"
  OutputPath: "DataModule/Cache/alvt_intraday_nasdaq"
  SymbolListPath: "symbol_list.csv"
  VSCurrency: "usd"
  Interval: "day"
  Length: -1
  CoinPool: 15000
  FetchLimit: 1500
  UseCache: true

# 执行流程配置
Execution:
  # 执行顺序，列出需要执行的收集器全名（数据源名.收集器名）
  Flow:
    # - CoinMarketCap.CoinMarketCapCollector
    # - MockSource.MockCollector 
    - AlphaVantage.AlphaVantageIntradayNasdaqCollector