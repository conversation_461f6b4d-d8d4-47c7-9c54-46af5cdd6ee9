import os
import pandas as pd
import polars as pl
import numpy as np
from scipy import stats
from typing import Union

from .common import BaseFactor

my_factor : BaseFactor = None

def init_factor(factor: BaseFactor):
    global my_factor
    my_factor = factor

def calculate_ic_and_rank_ic(df: pl.DataFrame, factor_col: str, price_col: str, periods: int = 1) -> pl.DataFrame:
    """
    计算 IC 和 Rank IC，并返回一个新的 DataFrame

    参数:
    df : pl.DataFrame
        输入的 DataFrame，包含因子和价格数据
    factor_col : str
        因子列名
    price_col : str
        价格列名
    periods : int
        预测期数，默认为1

    返回:
    pl.DataFrame : 包含 IC 和 Rank IC 的 DataFrame
    """
    forward_returns = df[price_col].pct_change(periods).shift(-periods)

    # filter nan
    # print(f"len of factor: {len(df[factor_col])}, len of forward_returns: {len(forward_returns)}")
    mask = (df[factor_col].is_not_nan() & forward_returns.is_not_nan())
    factor = df[factor_col].filter(mask)
    forward_returns = forward_returns.filter(mask)

    if len(factor) == 0 or len(forward_returns) == 0:
        return pl.DataFrame({"IC": [None], "Rank IC": [None]})
    # print(f"len of factor: {len(factor)}, len of forward_returns: {len(forward_returns)}")

    df_ic = pl.DataFrame({
        'factor': factor,
        'forward_returns': forward_returns
    })
    ic = df_ic.select(pl.corr('factor', 'forward_returns'))
    rank_ic = df_ic.select(pl.corr("factor", "forward_returns", method="spearman"))

    return pl.DataFrame({
        "IC": ic["factor"],
        "RankIC": rank_ic["factor"]
    })



def get_data(method: str, data:Union[str, pl.DataFrame] = None, param_li: list = None) -> pl.DataFrame:
    if method == "symbol":
        symbol = data
        file_path = f"/data/tmp/{symbol}.csv"
        return pl.read_csv(file_path)
    elif method == "param_li":
        symbol = data
        src = os.path.join(*param_li)
        file_path = f"{src}/{data}.csv"
        return pl.read_csv(file_path)
    else:
        raise ValueError(f"Unsupported get data format")


def cal_factor(df: pl.DataFrame) -> pl.DataFrame:
    if my_factor is None:
        raise ValueError(f"unknown factor")
    return my_factor.cal(df)


def cal_factor_ic(symbols: list, 
                           data_method: str,
                           **kwargs):
    param_li = None
    if data_method != "symbol":
        param_li = kwargs[data_method]
    results = []    
    for symbol in symbols:
        df = get_data(data_method, symbol, param_li)
        df = cal_factor(df)
        ic_result = calculate_ic_and_rank_ic(df, "factor", 'close', periods=1)
        ic_result = ic_result.with_columns(pl.lit(symbol).alias("symbol"))
        ic_result = ic_result.select(['symbol', 'IC', 'RankIC'])
        results.append(ic_result)

    all_ic_results = pl.concat(results)
    stats = all_ic_results.select([
        pl.col("IC").mean().alias("IC_Mean"),
        pl.col("RankIC").mean().alias("RankIC_Mean"),
        pl.col("IC").std().alias("IC_Std"),
        pl.col("RankIC").std().alias("RankIC_Std"),
        pl.col("IC").abs().min().alias("IC_abs_Min"),
        pl.col("RankIC").abs().min().alias("RankIC_abs_Min"),
        pl.col("IC").abs().max().alias("IC_abs_Max"),
        pl.col("RankIC").abs().max().alias("RankIC_abs_Max")
    ])
    return all_ic_results, stats
