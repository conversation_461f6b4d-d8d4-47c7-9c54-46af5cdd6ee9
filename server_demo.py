# 一个运行在本地8080端口的server
import http.server
import socketserver
import threading


class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器，返回Hello World From Windows"""

    def do_GET(self):
        """处理GET请求"""
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'Hello World From Windows')
        print(f"[Server] Received GET request from {self.client_address}")

    def log_message(self, format, *args):
        """重写日志方法，输出服务器日志"""
        print(f"[Server] {format % args}")


class Server:
    """简单的HTTP服务器类，监听本地8080端口"""

    def __init__(self, port=8080):
        """初始化服务器"""
        self.port = port
        self.server = None
        self.server_thread = None
        self.is_running = False

    def start(self):
        """启动服务器"""
        if self.is_running:
            print(f"服务器已经在端口 {self.port} 上运行")
            return

        try:
            # 创建HTTP服务器
            handler = CustomHTTPRequestHandler
            self.server = socketserver.TCPServer(("", self.port), handler)

            # 在新线程中启动服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()

            self.is_running = True
            print(f"服务器已启动，监听端口 {self.port}")
            print(f"可以通过 curl localhost:{self.port} 访问")
        except Exception as e:
            print(f"启动服务器时出错: {e}")

    def stop(self):
        """停止服务器"""
        if not self.is_running:
            print("服务器未运行")
            return

        try:
            self.server.shutdown()
            self.server.server_close()
            self.is_running = False
            print("服务器已停止")
        except Exception as e:
            print(f"停止服务器时出错: {e}")


# 示例用法
if __name__ == "__main__":
    server = Server()
    server.start()

    try:
        # 保持主线程运行
        import time
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        # 捕获Ctrl+C，优雅地关闭服务器
        print("\n正在关闭服务器...")
        server.stop()
