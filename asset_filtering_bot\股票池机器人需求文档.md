
# 加密资产选股机器人需求文档

## 一、系统概述

加密资产选股机器人是一个自动化系统，根据预设的筛选方法论对加密货币市场进行多维度分析，筛选出基本面稳固、流动性良好、风险相对可控的加密资产，适合中长期低频交易策略。

## 二、功能模块

### 1. 数据采集模块
- 从多个数据源获取加密货币的市场数据
- 获取链上数据、社区活跃度、开发活动等信息
- 定期更新数据库中的资产信息

### 2. 筛选引擎模块
- 实现五步筛选方法论的核心逻辑
- 支持自定义筛选参数和阈值
- 生成筛选报告与推荐结果

### 3. 风险评估模块
- 分析代币经济学模型
- 评估持币集中度
- 计算历史波动率
- 监控合规与安全风险

### 4. 用户界面模块
- 展示筛选结果和资产详情
- 提供参数配置界面
- 显示历史筛选结果和对比分析

### 5. 通知与报告模块
- 定期生成资产分析报告
- 发送重要市场变化提醒
- 提供资产池优化建议

## 三、接口设计

### 数据采集接口
```
interface DataCollectorService {
  // 获取前N名加密货币基本信息
  getTopCryptocurrencies(limit: number): Promise<CryptoBasicInfo[]>;
  
  // 获取资产详细市场数据
  getMarketData(assetId: string, timeRange: TimeRange): Promise<MarketData>;
  
  // 获取资产链上活动数据
  getOnChainData(assetId: string, metrics: string[]): Promise<OnChainData>;
  
  // 获取开发活动数据
  getDevelopmentActivity(assetId: string): Promise<DevelopmentData>;
  
  // 获取社区数据
  getCommunityData(assetId: string): Promise<CommunityData>;
}
```

### 筛选引擎接口
```
interface ScreeningEngineService {
  // 定义初始观察池
  defineUniverse(criteria: UniverseCriteria): Promise<Asset[]>;
  
  // 流动性与市场接受度筛选
  applyLiquidityFilter(assets: Asset[], criteria: LiquidityCriteria): Promise<Asset[]>;
  
  // 基本面质量评估
  applyFundamentalFilter(assets: Asset[], criteria: FundamentalCriteria): Promise<Asset[]>;
  
  // 风险因素排查
  applyRiskFilter(assets: Asset[], criteria: RiskCriteria): Promise<Asset[]>;
  
  // 构建最终资产池
  constructPortfolio(assets: Asset[], criteria: PortfolioCriteria): Promise<Portfolio>;
  
  // 运行完整筛选流程
  runFullScreeningProcess(parameters: ScreeningParameters): Promise<ScreeningResult>;
}
```

### 风险评估接口
```
interface RiskAssessmentService {
  // 分析代币经济学
  analyzeTokenomics(assetId: string): Promise<TokenomicsAnalysis>;
  
  // 评估持币集中度
  evaluateConcentration(assetId: string): Promise<ConcentrationMetrics>;
  
  // 计算历史波动率
  calculateVolatility(assetId: string, period: number): Promise<VolatilityMetrics>;
  
  // 检查安全风险
  checkSecurityRisks(assetId: string): Promise<SecurityRiskReport>;
  
  // 生成综合风险评分
  generateRiskScore(assetId: string): Promise<RiskScore>;
}
```

## 四、ER关系图

```
资产(Asset)
  ID
  名称
  代码
  简介
  创建时间
  官网
  1:N -> 市场数据(MarketData)
  1:N -> 链上数据(OnChainData)
  1:1 -> 代币经济学(Tokenomics)
  1:N -> 开发活动(DevelopmentActivity)

市场数据(MarketData)
  ID
  资产ID (外键)
  时间戳
  价格
  市值
  交易量
  流通量
  交易所列表

链上数据(OnChainData)
  ID
  资产ID (外键)
  时间戳
  活跃地址数
  交易数量
  网络费用
  总锁仓价值

代币经济学(Tokenomics)
  ID
  资产ID (外键)
  总供应量
  流通供应量
  通胀率
  分配方案
  解锁时间表

筛选标准(ScreeningCriteria)
  ID
  名称
  描述
  创建时间
  创建者
  参数配置
  1:N -> 筛选结果(ScreeningResult)

筛选结果(ScreeningResult)
  ID
  筛选标准ID (外键)
  执行时间
  通过资产列表
  筛选日志
  生成报告

用户(User)
  ID
  用户名
  密码哈希
  邮箱
  创建时间
  1:N -> 筛选标准(ScreeningCriteria)
  1:N -> 用户资产池(UserPortfolio)

用户资产池(UserPortfolio)
  ID
  用户ID (外键)
  名称
  创建时间
  更新时间
  资产列表
  权重分配
```

## 五、UML流程图

### 主筛选流程
```
开始
  |
  v
初始化筛选参数
  |
  v
数据采集模块获取Top100资产
  |
  v
定义初始观察池(Universe)
  |
  v
应用流动性筛选
  |  - 检查日均交易量>5000万美元
  |  - 确认至少3家主流交易所上市
  |  - 检查市值>5亿美元
  v
应用基本面质量评估
  |  - 评估技术与开发活跃度
  |  - 分析应用与生态系统
  |  - 评估团队与治理
  v
应用风险因素排查
  |  - 分析代币经济学
  |  - 检查持币集中度
  |  - 计算历史波动率
  |  - 排查合规与安全风险
  v
构建资产池
  |  - 综合评分排序
  |  - 行业分散配置
  v
生成筛选报告
  |
  v
通知用户
  |
  v
结束
```

### 定期更新流程
```
开始
  |
  v
定时触发
  |
  v
获取现有资产池
  |
  v
更新资产数据
  |
  v
重新执行筛选流程
  |
  v
比较新旧资产池
  |
  v
识别需移出资产
  |  - 不再满足筛选标准
  |  - 出现重大负面信号
  v
识别新入选资产
  |
  v
生成更新建议报告
  |
  v
通知用户
  |
  v
结束
```

## 六、关键实现考虑

1. 数据源集成：
   - 需要集成CoinMarketCap、CoinGecko的API
   - 接入Glassnode、Nansen等链上分析工具
   - 集成GitHub API分析开发活动

2. 性能考虑：
   - 建立数据缓存机制，避免频繁API调用
   - 使用异步处理大量数据分析任务
   - 实现增量更新机制，只处理变化的数据

3. 可扩展性：
   - 采用插件式架构，支持新的筛选指标和数据源
   - 提供自定义筛选条件的接口
   - 支持算法模型升级与替换

4. 安全性：
   - 实现数据加密存储
   - 建立API访问权限控制
   - 提供用户操作审计日志
