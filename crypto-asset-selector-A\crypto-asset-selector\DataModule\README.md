# DataModule

DataModule是一个用于收集加密货币数据的模块，支持从不同的数据源API收集数据。

## 项目结构

```
DataModule/
├── Collectors/                # Collector实现类
│   ├── __init__.py
│   ├── base_collector.py      # BaseCollector基类
│   ├── token_insight_coin_list_collector.py
│   └── token_insight_coin_history_collector.py
├── Cache/                     # 缓存数据目录
│   └── __init__.py
├── FetcherApi/                # API调用实现
│   ├── coin_list_fetcher.py
│   └── coin_history_fetcher.py
├── Utils/                     # 工具类
│   ├── __init__.py
│   └── env_loader.py
├── __init__.py
├── collector_registry.py      # Collector注册中心
├── data_config.yaml           # 配置文件
├── main.py                    # 入口文件
└── README.md                  # 说明文档
```

## 使用方法

### 1. 配置环境变量

在项目根目录创建`.data.env`文件，配置API相关的环境变量：

```
TOKEN_INSIGHT_API_KEY=your_api_key
TOKEN_INSIGHT_GET_COIN_LIST=https://api.tokeninsight.com/api/v1/coins
TOKEN_INSIGHT_GET_COIN_HISTORY=https://api.tokeninsight.com/api/v1/coins/
TOKEN_INSIGHT_API_LIMIT=60
```

### 2. 配置数据源

编辑`data_config.yaml`文件，配置数据源和Collector：

```yaml
DataSource:
  - Name: TokenInsight
    APIKey: xxxxxxxx
    Collectors:
      - TokenInsightCoinListCollector
      - TokenInsightCoinHistoryCollector
    Settings:
      workers: 12
      max_retries: 3
      api_limit: 60
      output_path: ./output/token_insight
      coins_cache_path: collected_coins.parquet
      symbol_list_path: symbol_list.csv
      coin_pool: 1500
      fetch_limit: 1500
      vs_currency: usd
      interval: day
      length: -1
      interval_output_path: collected_coins_interval.parquet
```

### 3. 运行数据收集

```bash
python main.py --config data_config.yaml
```

指定数据源：

```bash
python main.py --config data_config.yaml --data_source TokenInsight
```

## 扩展新的数据源

1. 创建新的Fetcher实现类，放在`FetcherApi`目录下
2. 创建新的Collector实现类，继承`BaseCollector`，放在`Collectors`目录下
3. 在`data_config.yaml`中添加新的数据源配置
4. 在`Collectors/__init__.py`中导入新的Collector类

例如，添加CoinGecko数据源：

1. 创建`FetcherApi/coin_gecko_list_fetcher.py`和`FetcherApi/coin_gecko_history_fetcher.py`
2. 创建`Collectors/coin_gecko_coin_list_collector.py`和`Collectors/coin_gecko_coin_history_collector.py`
3. 在`data_config.yaml`中添加CoinGecko数据源配置
4. 在`Collectors/__init__.py`中导入新的Collector类

## Collector设计

BaseCollector使用模板模式设计，提供了通用的数据收集流程：

```python
def run(self, params):
    self.pre_handle(params)
    result = self.handle(params)
    return self.post_handle(result)
```

- `pre_handle`: 预处理阶段，在主处理前执行
- `handle`: 主处理阶段，执行核心业务逻辑
- `post_handle`: 后处理阶段，在主处理后执行

具体的Collector实现类需要实现这些方法，提供特定的业务逻辑。
