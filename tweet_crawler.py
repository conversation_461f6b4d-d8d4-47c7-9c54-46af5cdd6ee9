import requests
from bs4 import BeautifulSoup
from textblob import TextBlob
import re
import json

class TweetCrawler:
    def __init__(self, html_path):
        self.base_url = 'http://localhost:8000'  # 本地调试用临时地址
        self.html_path = html_path
        
    def load_local_html(self):
        with open(self.html_path, 'r', encoding='utf-8') as f:
            return BeautifulSoup(f.read(), 'html.parser')

    def extract_tweet_data(self, soup):
        tweet_pattern = re.compile(r'data-tweet-id="(\d+)"') 
        tweets = []
        tweet_divs = soup.find_all('div', {'class': 'tweet'})
        for div in tweet_divs:
            tweet_id = div.get('data-tweet-id', '')
            text_div = div.find('div', {'class': 'tweet-text'})
            content = text_div.get_text(strip=True) if text_div else ''
            user_tag = div.find('span', {'class': 'username'})
            user_info = user_tag.get_text(strip=True) if user_tag else ''
            stats = {
                'retweets': div.find('span', {'class': 'retweet-count'}).text if div.find('span', {'class': 'retweet-count'}) else 0,
                'likes': div.find('span', {'class': 'like-count'}).text if div.find('span', {'class': 'like-count'}) else 0
            }
            emojis = [img['alt'] for img in div.find_all('img', {'class': 'emoji'})]
            
            tweets.append({
                'id': tweet_id,
                'content': content,
                'user': user_info,
                'engagement': stats,
                'emojis': emojis,
                'sentiment': self.sentiment_analysis(content)
            })
        return tweets

    def sentiment_analysis(self, text):
        analysis = TextBlob(text)
        return {
            'polarity': analysis.sentiment.polarity,
            'subjectivity': analysis.sentiment.subjectivity
        }

if __name__ == '__main__':
    crawler = TweetCrawler('d:\\Work\\X-GPT.html')
    soup = crawler.load_local_html()
    print('抓取到', len(crawler.extract_tweet_data(soup)), '条推文')