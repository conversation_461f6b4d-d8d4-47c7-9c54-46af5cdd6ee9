import sys
import polars as pl
sys.path.append("/data/code")

from quant import cal_factor_ic, BaseFactor, init_factor

class MyFactor(BaseFactor):

    '''helper function'''
    def _ema_span(self, df: pl.DataFrame, col_name: str, span: int=5):
        return df.with_columns((df[col_name].ewm_mean(span = span)).alias("tmp_result"))

    ''' factor'''
    def 振幅(self, df: pl.DataFrame):
        return df.with_columns((df["high"] / df["low"] - 1).alias("factor"))

    def 波动范围(self, df: pl.DataFrame):
        return df.with_columns(((df["high"] - df["low"]) / df["open"]).alias("factor"))

    def N日波动率(self, df: pl.DataFrame, n=5):
        # 计算收益
        # df = df.with_columns(((df["close"] - df["open"]) / df["open"]).alias("factor"))
        # 计算振幅
        df = self.振幅(df)
        # 计算波动范围
        # df = self.波动范围(df)
        return df.with_columns((df["factor"].rolling_std(window_size=n, min_periods=1)).alias("factor"))

    def MACD(self, df: pl.DataFrame, short=12, long=26, m=9):
        # 计算短期EMA（tmp_result_1）
        df = self._ema_span(df, col_name="close", span=short).rename({"tmp_result": "ema_short"})
        # 计算长期EMA（tmp_result_2）
        df = self._ema_span(df, col_name="close", span=long).rename({"tmp_result": "ema_long"})
        # 计算DIF并添加为列
        df = df.with_columns((pl.col("ema_short") - pl.col("ema_long")).alias("dif"))
        # 计算DEA（DIF的EMA）
        df = self._ema_span(df, col_name="dif", span=m).rename({"tmp_result": "dea"})
        # 计算MACD柱（DIF - DEA）
        df = df.with_columns((pl.col("dif") - pl.col("dea")).alias("factor"))
        return df



    def cal(self, df: pl.DataFrame):
        return self.MACD(df)


init_factor(MyFactor())

path_param = ["/", "data", "kline_okx", "perp", "30m"]
all_ic_results, detailed_stats = cal_factor_ic(symbols=['BTC_USDT', 'TRUMP_USDT'], data_method = "param_li", param_li = path_param)
print("\nIC值:")
print(all_ic_results)
# print("\n详细统计信息:")
# print(detailed_stats)