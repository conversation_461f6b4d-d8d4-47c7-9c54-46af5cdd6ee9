import aiohttp
import asyncio
import polars as pl
import argparse
import os
from env_loader import TOKEN_INSIGHT_GET_COIN_LIST, get_api_headers

async def fetch_coin_list(limit=1500, offset=0, vs_currency="usd"):
    """
    异步获取币种列表数据

    参数:
    limit (int): 返回的币种数量，默认1500，最大1500
    offset (int): 偏移量，默认0
    vs_currency (str): 计价货币，默认为usd

    返回:
    polars.DataFrame: 包含币种信息的DataFrame
    """
    # 使用环境变量中的API URL
    url = TOKEN_INSIGHT_GET_COIN_LIST
    params = {
        "limit": limit,
        "offset": offset,
        "vs_currency": vs_currency
    }

    # 获取包含API KEY的headers
    headers = get_api_headers()

    # 添加额外的headers，模拟requests库的行为
    headers.update({
        'User-Agent': 'python-requests/2.28.1',
        'Accept-Encoding': 'gzip, deflate',
        'Accept': '*/*',
        'Connection': 'keep-alive'
    })

    print(f"完整headers: {headers}")

    try:
        # 创建ClientSession时设置更多选项
        async with aiohttp.ClientSession(
            trust_env=True,  # 信任环境变量中的代理设置
            connector=aiohttp.TCPConnector(ssl=False)  # 禁用SSL验证，如果需要的话
        ) as session:
            async with session.get(url, params=params, headers=headers) as response:
                print(f"响应状态码: {response.status}")
                response.raise_for_status()
                data = await response.json()

                if data["status"]["code"] == 0:
                    coins_data = data["data"]["items"]

                    # 提取所需列
                    df = pl.DataFrame([{
                        "price": coin.get("price"),
                        "symbol": coin.get("symbol"),
                        "id": coin.get("id"),
                        "spot_volume_24h": coin.get("spot_volume_24h"),
                        "price_change_percentage_24h": coin.get("price_change_percentage_24h")
                    } for coin in coins_data])

                    return df
                else:
                    raise Exception(f"API调用错误: {data['status']['message']}")

    except Exception as e:
        print(f"获取币种列表时出错: {e}")
        return None

def save_to_parquet(df, output_path="collected_coins.parquet"):
    """
    将DataFrame保存为parquet文件

    参数:
    df (polars.DataFrame): 要保存的DataFrame
    output_path (str): 输出文件路径
    """
    try:
        df.write_parquet(output_path)
        print(f"数据已保存到 {output_path}")
    except Exception as e:
        print(f"保存数据时出错: {e}")

def filter_by_symbol_list(df, symbol_list_path="symbol_list.csv"):
    """
    通过symbol_list.csv过滤币种

    参数:
    df (polars.DataFrame): 要过滤的DataFrame
    symbol_list_path (str): symbol列表文件路径

    返回:
    polars.DataFrame: 过滤后的DataFrame
    """
    try:
        if not os.path.exists(symbol_list_path):
            print(f"错误: 未找到符号列表文件 {symbol_list_path}")
            return df

        symbol_df = pl.read_csv(symbol_list_path)
        # 过滤币种:选出symbol_list.csv中有的币种，如果df中有多个重复的symbol，则保留spot_volume_24h最大的那一个
        filtered_df = df.filter(pl.col("symbol").is_in(symbol_df["symbol"])).sort("spot_volume_24h", descending=True).unique(subset=["symbol"], keep="first")


        print(f"通过符号列表过滤后，币种数量从 {len(df)} 减少到 {len(filtered_df)}")
        return filtered_df

    except Exception as e:
        print(f"过滤币种时出错: {e}")
        return df

async def async_main():
    parser = argparse.ArgumentParser(description="获取加密货币列表")
    parser.add_argument("--limit", type=int, default=1500, help="返回的币种数量，默认1500，最大1500")
    parser.add_argument("--offset", type=int, default=0, help="偏移量，默认0")
    parser.add_argument("--vs_currency", type=str, default="usd", help="计价货币，默认为usd")
    parser.add_argument("--output", type=str, default="collected_coins.parquet", help="输出文件路径")
    parser.add_argument("--filter", action="store_true", help="是否使用symbol_list.csv过滤币种")
    parser.add_argument("--symbol_list", type=str, default="symbol_list.csv", help="symbol列表文件路径")
    parser.add_argument("--save_full", action="store_true", help="是否保存未过滤的完整币种列表")

    args = parser.parse_args()

    # 异步获取币种列表
    df = await fetch_coin_list(args.limit, args.offset, args.vs_currency)
    if args.save_full and df is not None and not df.is_empty():
        save_to_parquet(df, "collected_coins_full.parquet")

    if df is not None and not df.is_empty():
        # 如果需要过滤
        if args.filter:
            df = filter_by_symbol_list(df, args.symbol_list)

        # 保存到parquet
        save_to_parquet(df, args.output)
    else:
        print("获取币种列表失败或返回空列表")

def main():
    """
    主函数，运行异步任务
    """
    asyncio.run(async_main())

if __name__ == "__main__":
    main()