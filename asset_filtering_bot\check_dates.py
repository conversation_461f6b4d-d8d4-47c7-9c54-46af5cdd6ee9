#!/usr/bin/env python
# -*- coding: utf-8 -*-

import polars as pl
from datetime import datetime, timedelta

# 读取parquet文件
df = pl.read_parquet("history_mkc.parquet")

# 将日期字符串转换为日期对象
dates = df["date"].sort().to_list()
date_objects = [datetime.strptime(d, "%Y-%m-%d").date() for d in dates]

# 检查日期是否连续
first_date = date_objects[0]
last_date = date_objects[-1]
all_dates = [(first_date + timedelta(days=i)) for i in range((last_date - first_date).days + 1)]

# 找出缺失的日期
missing_dates = set(all_dates) - set(date_objects)

print(f"总日期数: {len(dates)}")
print(f"日期范围: {first_date} 到 {last_date}")
print(f"理论上应有日期数: {len(all_dates)}")

if missing_dates:
    print(f"缺失的日期数: {len(missing_dates)}")
    print("缺失的日期:")
    for d in sorted(missing_dates):
        print(d.isoformat())
else:
    print("日期是连续的，没有缺失")