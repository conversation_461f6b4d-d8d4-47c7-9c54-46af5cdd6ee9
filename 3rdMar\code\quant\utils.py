from abc import ABC, abstractmethod
from inspect import signature
from typing import Dict, Callable, Any, List, Union
from types import MappingProxyType

def get_param_dict(func):
    return {param.name: None for param in signature(func).parameters.values()}

class Cache(ABC):
    def __init__(self):
        self.meta = {}
        self.data = {}


class FunctionExecutor:
    def __init__(self):
        self.order_li: List[str] = []
        self._functions: Dict[str, Callable[..., None]] = {}
        self._params: Dict[str, Dict[str, Any]] = {}
        self.functions = MappingProxyType(self._functions)
        self.params = MappingProxyType(self._params)

    def register_function(self, func_name: str, func: Callable[..., None], params: Dict[str, Any] = None):
        if func_name in self._functions:
            raise ValueError(f"函数名已存在: {func_name}")
        self.order_li.append(func_name)
        self._functions[func_name] = func
        self._params[func_name] = params

    def execute_function(self, func_name: str):
        return self.functions[func_name](**self.params[func_name])
    
    def execute_functions(self):
        for func_name in self.order_li:
            self.functions[func_name](**self.params[func_name])

    def update_param(self, func_name: str, kwargs:dict):
        self._params[func_name].update({key: kwargs[key] for key in self._params[func_name].keys() if key in kwargs})
