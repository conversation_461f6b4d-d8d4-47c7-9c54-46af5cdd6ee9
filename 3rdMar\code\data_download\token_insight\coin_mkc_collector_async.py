import asyncio
import os
import time
import argparse
import polars as pl
from typing import List, Dict, Tuple, Optional, Set
from collections import deque
import logging

# 导入现有模块
from coin_list_fetcher import fetch_coin_list, filter_by_symbol_list, save_to_parquet
from coin_history_fetcher import fetch_coin_history
from env_loader import TOKEN_INSIGHT_API_LIMIT
from retry_queue import RetryQueue

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("coin_collector.log")
    ]
)
logger = logging.getLogger(__name__)

class CoinMkcCollectorAsync:
    """
    异步加密货币数据收集器
    使用asyncio进行并发网络请求，获取加密货币的历史数据
    """
    def __init__(self,
                 concurrency: int = 5,
                 coins_cache_path: str = "collected_coins.parquet",
                 symbol_list_path: str = "symbol_list.csv",
                 interval_output_path: str = "collected_coins_interval.parquet",
                 output_dir: str = "./coins_history",
                 interval: str = "day",
                 vs_currency: str = "usd",
                 max_retries: int = 3,
                 coin_pool: int = 15704,
                 fetch_limit: int = 1500,
                 rate_limit: int = TOKEN_INSIGHT_API_LIMIT):
        """
        初始化收集器

        参数:
        concurrency (int): 并发请求数量
        coins_cache_path (str): 币种缓存文件路径
        symbol_list_path (str): 符号列表文件路径
        interval_output_path (str): 间隔数据输出文件路径
        output_dir (str): 输出数据存放目录
        interval (str): 时间间隔，可选值为minute、hour、day
        vs_currency (str): 计价货币
        max_retries (int): 最大重试次数
        rate_limit (int): API请求频率限制(每分钟)
        """
        self.concurrency = concurrency
        self.coins_cache_path = coins_cache_path
        self.symbol_list_path = symbol_list_path
        self.interval_output_path = interval_output_path
        self.output_dir = output_dir
        self.interval = interval
        self.vs_currency = vs_currency
        self.max_retries = max_retries
        self.rate_limit = rate_limit
        self.coin_pool = min(coin_pool, 15704)  # 最大支持约15704个币种
        self.fetch_limit = min(fetch_limit, 1500)  # 单次请求最大1500个币种

        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)

        # 用于存储币种时间戳信息的数据
        self.coin_intervals = []

        # 用于限制API请求频率的信号量
        self.rate_limit_semaphore = asyncio.Semaphore(self.concurrency)

        # 用于记录API请求时间的队列
        self.request_times = deque(maxlen=self.rate_limit)

        # 创建重试队列
        self.retry_queue = RetryQueue(
            max_retries=self.max_retries,
            retry_delay=0.5,
            max_concurrent_retries=self.concurrency,
            enable_exponential_backoff=True
        )

    async def get_coin_list(self) -> Optional[pl.DataFrame]:
        """
        获取符合条件的币种列表

        返回:
        Optional[pl.DataFrame]: 过滤后的币种DataFrame，如果失败则返回None
        """
        # 如果本地已缓存，直接读取并过滤
        if os.path.exists(self.coins_cache_path):
            logger.info(f"使用本地缓存的币种列表: {self.coins_cache_path}")
            df = pl.read_parquet(self.coins_cache_path)
            # 对缓存的数据也进行过滤
            return filter_by_symbol_list(df, self.symbol_list_path)

        # 否则，从API获取币种列表
        logger.info("开始从API获取币种列表")

        batch_count = 0
        all_coins_df = None
        offset = 0
        tasks = []

        total_batch = (self.coin_pool + self.fetch_limit - 1) // self.fetch_limit


        async def fetch_batch(offset, limit):
            # async with self.rate_limit_semaphore:
            await self.wait_for_rate_limit()
            df = await fetch_coin_list(limit=limit, offset=offset, vs_currency=self.vs_currency)
            return df, offset

        while offset < self.coin_pool:
            batch_count += 1
            logger.info(f"获取币种批次 {batch_count}/{total_batch}入队，偏移量: {offset}")

            # 计算当前批次应获取的数量
            current_limit = min(self.fetch_limit, self.coin_pool - offset)

            # 获取当前批次的币种列表
            task = asyncio.create_task(fetch_batch(offset, current_limit))

            tasks.append(task)

            # 更新偏移量
            offset += self.fetch_limit

        logger.info("所有批次入队完毕，开始并发获取")
        for future in asyncio.as_completed(tasks):
            batch_df, offset = await future
            if batch_df is not None and not batch_df.is_empty():
                if all_coins_df is None:
                    all_coins_df = batch_df
                else:
                    all_coins_df = pl.concat([all_coins_df, batch_df])
            else:
                logger.warning(f"获取币种批次 +{offset} 失败或返回空列表，进入重试队列")
                task = asyncio.create_task(fetch_batch(offset, current_limit))
                self.retry_queue.put_nowait(task)


        if all_coins_df is not None and not all_coins_df.is_empty():
            logger.info(f"共获取 {len(all_coins_df)} 个币种")

            # 过滤并保存
            filtered_df = filter_by_symbol_list(all_coins_df, self.symbol_list_path)
            save_to_parquet(filtered_df, self.coins_cache_path)
            return filtered_df

        logger.error("获取币种列表失败")
        return None

    async def wait_for_rate_limit(self):
        """
        等待API请求频率限制
        确保请求不超过每分钟的限制
        """
        now = time.time()

        # 如果队列已满且最早的请求在一分钟内，则等待
        if (len(self.request_times) >= self.rate_limit and
            now - self.request_times[0] < 60):
            wait_time = 60 - (now - self.request_times[0])
            logger.debug(f"API请求频率限制，等待 {wait_time:.2f} 秒")
            await asyncio.sleep(wait_time)

        # 记录当前请求时间
        self.request_times.append(time.time())



    async def fetch_coin_history_task(self, coin_id: str, symbol: str) -> Tuple[bool, Dict]:
        """
        获取币种历史数据的任务函数（不包含重试逻辑）

        参数:
        coin_id (str): 币种ID
        symbol (str): 币种符号

        返回:
        Tuple[bool, Dict]: (是否成功, 币种数据)
        """
        try:
            async with self.rate_limit_semaphore:
                # 等待API请求频率限制
                await self.wait_for_rate_limit()

                logger.info(f"正在获取 {symbol}({coin_id}) 的历史数据...")

                # 调用现有模块获取历史数据
                df, symbol, first_timestamp, last_timestamp, current_market_cap = await fetch_coin_history(
                    coin_id, interval=self.interval, length=-1, vs_currency=self.vs_currency
                )

                if df is None or df.is_empty():
                    logger.warning(f"{symbol}({coin_id}) 没有历史数据或获取失败")
                    return False, {"coin_id": coin_id, "symbol": symbol}

                # 保存历史数据
                output_path = os.path.join(self.output_dir, f"{symbol}_{coin_id}_history_{first_timestamp}_{last_timestamp}.parquet")
                df.write_parquet(output_path)
                logger.info(f"数据已保存到 {output_path}")

                # 记录时间戳信息（需要线程安全）
                self.coin_intervals.append({
                    "symbol": symbol,
                    "id": coin_id,
                    "start_timestamp": first_timestamp,
                    "end_timestamp": last_timestamp,
                    "current_market_cap": current_market_cap
                })

                return True, {
                    "coin_id": coin_id,
                    "symbol": symbol,
                    "first_timestamp": first_timestamp,
                    "last_timestamp": last_timestamp,
                    "current_market_cap": current_market_cap
                }

        except Exception as e:
            logger.error(f"获取 {symbol}({coin_id}) 历史数据时出错: {e}")
            return False, {"coin_id": coin_id, "symbol": symbol}

    async def fetch_coin_history_with_retry(self, coin_id: str, symbol: str) -> Tuple[bool, Dict]:
        """
        获取币种历史数据，支持重试（包装函数）

        参数:
        coin_id (str): 币种ID
        symbol (str): 币种符号

        返回:
        Tuple[bool, Dict]: (是否成功, 币种数据)
        """
        # 首次尝试
        success, result = await self.fetch_coin_history_task(coin_id, symbol)

        # 如果失败，添加到重试队列
        if not success:
            await self.retry_queue.add_task(
                task_func=self.fetch_coin_history_task,
                task_args=(coin_id, symbol),
                task_id=f"{symbol}_{coin_id}",
                metadata={"coin_id": coin_id, "symbol": symbol}
            )

        return success, result

    async def save_intervals_data(self):
        """保存币种时间戳信息"""
        if not self.coin_intervals:
            logger.warning("没有币种时间戳信息可保存")
            return

        try:
            # 创建DataFrame并保存
            intervals_df = pl.DataFrame(self.coin_intervals)
            intervals_df.write_parquet(self.interval_output_path)
            logger.info(f"币种时间戳信息已保存到 {self.interval_output_path}")
        except Exception as e:
            logger.error(f"保存币种时间戳信息时出错: {e}")

    async def run(self):
        """运行收集器"""
        start_time = time.time()
        logger.info("开始运行加密货币数据收集器")

        # 获取币种列表
        coins_df = await self.get_coin_list()

        if coins_df is None or coins_df.is_empty():
            logger.error("没有可用的币种列表，退出")
            return

        # 提取币种ID和符号
        coins_data = coins_df.select(["id", "symbol"]).to_dicts()
        total_coins = len(coins_data)
        logger.info(f"共有 {total_coins} 个币种需要处理")

        # 创建任务
        tasks = []
        for coin in coins_data:
            task = self.fetch_coin_history_with_retry(coin["id"], coin["symbol"])
            tasks.append(task)

        # 启动后台重试队列处理器
        retry_processor = await self.retry_queue.start_background_processor()

        # 执行所有任务
        results = await asyncio.gather(*tasks)

        # 等待重试队列处理完毕
        await self.retry_queue.wait_for_completion()
        retry_processor.cancel()

        # 统计结果
        successful = sum(1 for success, _ in results if success)
        failed = total_coins - successful

        # 获取重试队列统计信息
        retry_stats = self.retry_queue.get_stats()

        # 保存时间戳信息
        await self.save_intervals_data()

        # 计算总耗时
        elapsed_time = time.time() - start_time
        logger.info(f"数据收集完成，耗时: {elapsed_time:.2f} 秒")
        logger.info(f"首次尝试 - 成功: {successful}, 失败: {failed}")
        logger.info(f"重试统计 - 总重试: {retry_stats['total_retries']}, 重试成功: {retry_stats['successful_retries']}, 重试失败: {retry_stats['failed_retries']}")
        logger.info(f"最终统计 - 总成功: {successful + retry_stats['successful_retries']}, 总失败: {retry_stats['failed_retries']}")

async def async_main():
    """异步主函数"""
    parser = argparse.ArgumentParser(description="异步加密货币数据收集器")
    parser.add_argument("--concurrency", type=int, default=5, help="并发请求数量")
    parser.add_argument("--coins_cache", type=str, default="collected_coins.parquet", help="币种缓存文件路径")
    parser.add_argument("--symbol_list", type=str, default="symbol_list.csv", help="符号列表文件路径")
    parser.add_argument("--interval_output", type=str, default="collected_coins_interval.parquet", help="间隔数据输出文件路径")
    parser.add_argument("--output_dir", type=str, default="./coins_history", help="输出数据存放目录")
    parser.add_argument("--interval", type=str, default="day", choices=["minute", "hour", "day"], help="时间间隔")
    parser.add_argument("--vs_currency", type=str, default="usd", help="计价货币")
    parser.add_argument("--max_retries", type=int, default=3, help="最大重试次数")
    parser.add_argument("--rate_limit", type=int, default=TOKEN_INSIGHT_API_LIMIT, help="API请求频率限制(每分钟)")
    parser.add_argument("--total_coins", type=int, default=16000, help="要获取的最大币种数量，默认16000")
    parser.add_argument("--batch_size", type=int, default=1500, help="每批次获取的币种数量，默认1500，最大1500")

    args = parser.parse_args()

    collector = CoinMkcCollectorAsync(
        concurrency=args.concurrency,
        coins_cache_path=args.coins_cache,
        symbol_list_path=args.symbol_list,
        interval_output_path=args.interval_output,
        output_dir=args.output_dir,
        interval=args.interval,
        vs_currency=args.vs_currency,
        max_retries=args.max_retries,
        rate_limit=args.rate_limit,
        total_coins=args.total_coins,
        batch_size=args.batch_size
    )

    await collector.run()

def main():
    """主函数，运行异步任务"""
    asyncio.run(async_main())

if __name__ == "__main__":
    main()
