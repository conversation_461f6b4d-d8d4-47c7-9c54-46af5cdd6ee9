import os
import requests
import polars as pl
from typing import Dict, Any, List, Optional

from .base_collector import BaseCollector
from DataModule.Utils.env_loader import TOKEN_INSIGHT_GET_COIN_LIST, get_api_headers

class TokenInsightCoinListCollector(BaseCollector):
    """TokenInsight币种列表收集器"""
    
    def __init__(self, 
                 workers: int = 12, 
                 max_retries: int = 3, 
                 api_limit: int = 60,
                 api_key: str = None,
                 cache_path: str = "DataModule/Cache/collected_coins.parquet",
                 symbol_list_path: str = "symbol_list.csv",
                 vs_currency: str = "usd"):
        """
        初始化TokenInsight币种列表收集器
        
        参数:
        workers (int): 线程池工作线程数
        max_retries (int): 最大重试次数
        api_limit (int): API每分钟请求数限制
        cache_path (str): 币种缓存文件路径
        symbol_list_path (str): 符号列表文件路径
        vs_currency (str): 计价货币
        """
        super().__init__(workers, max_retries, api_limit, api_key)
        self.cache_path = cache_path
        self.symbol_list_path = symbol_list_path
        self.vs_currency = vs_currency
        self.fetch_limit = 1500  # TokenInsight API限制每次最多获取1500个币种
    
    def pre_handle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理参数，检查是否有缓存
        
        参数:
        params (Dict): 原始参数
        
        返回:
        Dict: 预处理后的参数
        """
        # 从参数中获取相关设置，如果没有则使用默认值
        params["cache_path"] = params.get("cache_path", self.cache_path)
        params["symbol_list_path"] = params.get("symbol_list_path", self.symbol_list_path)
        params["vs_currency"] = params.get("vs_currency", self.vs_currency)
        params["coin_pool"] = params.get("coin_pool", 15000)  # 默认获取15000个币种
        params["fetch_limit"] = params.get("fetch_limit", self.fetch_limit)
        params["use_cache"] = params.get("use_cache", True)  # 默认使用缓存
        
        # 如果指定使用缓存且缓存文件存在，则直接使用缓存
        if params["use_cache"] and os.path.exists(params["cache_path"]):
            print(f"使用本地缓存的币种列表: {params['cache_path']}")
            params["use_local_cache"] = True
        else:
            params["use_local_cache"] = False
        
        return params
    
    def handle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理币种列表获取
        
        参数:
        params (Dict): 预处理后的参数
        
        返回:
        Dict: 处理结果
        """
        result = {
            "success": False,
            "data": None,
            "message": ""
        }
        
        # 如果使用本地缓存
        if params["use_local_cache"]:
            try:
                df = pl.read_parquet(params["cache_path"])
                filtered_df = self._filter_by_symbol_list(df, params["symbol_list_path"])
                result["success"] = True
                result["data"] = filtered_df
                result["message"] = f"从缓存加载了 {len(filtered_df)} 个币种"
                return result
            except Exception as e:
                print(f"读取缓存出错: {e}")
                # 如果缓存读取失败，继续尝试从API获取
        
        # 从API获取币种列表
        try:
            all_coins_df = self._fetch_all_coins(params)
            
            if all_coins_df is not None and not all_coins_df.is_empty():
                # 过滤并保存
                filtered_df = self._filter_by_symbol_list(all_coins_df, params["symbol_list_path"])
                
                # 保存到缓存
                os.makedirs(os.path.dirname(params["cache_path"]), exist_ok=True)
                filtered_df.write_parquet(params["cache_path"])
                
                result["success"] = True
                result["data"] = filtered_df
                result["message"] = f"获取并过滤了 {len(filtered_df)} 个币种"
            else:
                result["message"] = "未获取到币种列表或返回空列表"
        except Exception as e:
            result["message"] = f"获取币种列表出错: {e}"
        
        return result
    
    def post_handle(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        后处理结果
        
        参数:
        result (Dict): 处理结果
        
        返回:
        Dict: 后处理后的结果
        """
        # 如果有需要的额外处理可以在这里添加
        return result
    
    def _fetch_all_coins(self, params: Dict[str, Any]) -> Optional[pl.DataFrame]:
        """
        分批次获取所有币种
        
        参数:
        params (Dict): 参数
        
        返回:
        pl.DataFrame: 币种DataFrame，如果失败则返回None
        """
        print(f"开始获取币种列表，目标数量: {params['coin_pool']}")
        
        all_coins_df = None
        offset = 0
        batches_count = 0
        
        # 计算需要获取的批次数
        total_batches = (params["coin_pool"] + params["fetch_limit"] - 1) // params["fetch_limit"]
        
        while offset < params["coin_pool"]:
            batches_count += 1
            print(f"获取币种批次 {batches_count}/{total_batches}，偏移量: {offset}")
            
            # 检查并遵守API请求限制
            self.check_and_update_rate_limit()
            
            # 计算当前批次应获取的数量
            current_limit = min(params["fetch_limit"], params["coin_pool"] - offset)
            
            # 获取当前批次的币种列表
            batch_df = self._fetch_coin_list(limit=current_limit, offset=offset, vs_currency=params["vs_currency"])
            
            if batch_df is None or batch_df.is_empty():
                print(f"获取币种批次 {batches_count} 失败或返回空列表，将在后续重试")
                # 记录失败的请求参数，以便后续重试
                self.retry_queue.put({
                    "limit": current_limit,
                    "offset": offset,
                    "vs_currency": params["vs_currency"],
                    "batch_num": batches_count,
                    "retry_count": 0
                })
                # 继续处理下一批次，而不是中断整个过程
                offset += params["fetch_limit"]
                continue
            
            print(f"批次 {batches_count} 获取到 {len(batch_df)} 个币种")
            
            # 合并数据
            if all_coins_df is None:
                all_coins_df = batch_df
            else:
                all_coins_df = pl.concat([all_coins_df, batch_df])
            
            # 更新偏移量
            offset += params["fetch_limit"]
            
            # 如果返回的数据少于请求的数量，说明已经获取完所有可用的币种
            if len(batch_df) < current_limit:
                print(f"已获取所有可用币种，总数: {len(all_coins_df)}")
                break
        
        # 处理重试队列
        retry_count = 0
        while not self.retry_queue.empty():
            retry_params = self.retry_queue.get()
            batch_df = self._fetch_coin_list(
                limit=retry_params["limit"], 
                offset=retry_params["offset"], 
                vs_currency=retry_params["vs_currency"]
            )
            
            if batch_df is not None and not batch_df.is_empty():
                if all_coins_df is None:
                    all_coins_df = batch_df
                else:
                    all_coins_df = pl.concat([all_coins_df, batch_df])
            else:
                retry_params["retry_count"] += 1
                if retry_params["retry_count"] < self.max_retries:
                    self.retry_queue.put(retry_params)
                    
            retry_count += 1
            
            if retry_count >= 100:  # 防止无限循环
                break
        
        if all_coins_df is not None:
            print(f"共获取 {len(all_coins_df)} 个币种")
        
        return all_coins_df
    
    def _fetch_coin_list(self, limit: int = 1500, offset: int = 0, vs_currency: str = "usd") -> Optional[pl.DataFrame]:
        """
        获取币种列表数据
        
        参数:
        limit (int): 返回的币种数量，默认1500，最大1500
        offset (int): 偏移量，默认0
        vs_currency (str): 计价货币，默认为usd
        
        返回:
        pl.DataFrame: 包含币种信息的DataFrame
        """
        # 使用环境变量中的API URL
        url = TOKEN_INSIGHT_GET_COIN_LIST
        params = {
            "limit": limit,
            "offset": offset,
            "vs_currency": vs_currency
        }
        
        # 获取包含API KEY的headers
        headers = get_api_headers()
        
        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            if data["status"]["code"] == 0:
                coins_data = data["data"]["items"]
                
                # 提取所需列
                df = pl.DataFrame([{
                    "price": coin.get("price"),
                    "symbol": coin.get("symbol"),
                    "id": coin.get("id"),
                    "spot_volume_24h": coin.get("spot_volume_24h"),
                    "price_change_percentage_24h": coin.get("price_change_percentage_24h")
                } for coin in coins_data])
                
                return df
            else:
                raise Exception(f"API调用错误: {data['status']['message']}")
        
        except Exception as e:
            print(f"获取币种列表时出错: {e}")
            return None
    
    def _filter_by_symbol_list(self, df: pl.DataFrame, symbol_list_path: str) -> pl.DataFrame:
        """
        通过symbol_list.csv过滤币种
        
        参数:
        df (pl.DataFrame): 要过滤的DataFrame
        symbol_list_path (str): symbol列表文件路径
        
        返回:
        pl.DataFrame: 过滤后的DataFrame
        """
        try:
            if not os.path.exists(symbol_list_path):
                print(f"警告: 未找到符号列表文件 {symbol_list_path}")
                return df
            
            symbol_df = pl.read_csv(symbol_list_path)
            # 过滤币种
            filtered_df = df.filter(pl.col("symbol").is_in(symbol_df["symbol"]))
            
            print(f"通过符号列表过滤后，币种数量从 {len(df)} 减少到 {len(filtered_df)}")
            return filtered_df
        
        except Exception as e:
            print(f"过滤币种时出错: {e}")
            return df 