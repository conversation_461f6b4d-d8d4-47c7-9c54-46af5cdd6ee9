from .http import async_post_request

import hashlib, hmac
import time, json
import base64


BOTS = {}


class BaseBot():
    def send(self, text: str):
        raise NotImplementedError("This method should be implemented by subclasses.")
    async def async_send(self, text: str):
        raise NotImplementedError("This method should be implemented by subclasses.")



class FeishuBot(BaseBot):
    def __init__(self, name, webhook_url, secret, label):
        self.name = name
        self.webhook_url = webhook_url
        self.secret = secret
        self.label = label
        
    def _sign(self, timestamp, secret):
        string_to_sign = '{}\n{}'.format(timestamp, secret)
        hmac_code = hmac.new(string_to_sign.encode("utf-8"), digestmod=hashlib.sha256).digest()
        sign = base64.b64encode(hmac_code).decode('utf-8')
        return sign

    async def async_send(self, text: str, label: dict=None):
        if label is None:
            label = self.label
        try:
            timestamp = str(int(time.time()))
            sign = self._sign(timestamp, self.secret)
            headers = {"Content-Type": "text/plain"}
            payload = {
                "timestamp": timestamp,
                "sign": sign,
                "msg_type": "text",
                "content": {
                    "text": f"{json.dumps(label)}\n{text}"
                }
            }
            response = await async_post_request(url=self.webhook_url, headers=headers, data=payload)
            print(f"Response: {response.code} {response.reason}")
            # TODO chech response
            return
        except Exception as e:
            # TODO: use log here maybe
            pass




def get_bot(bot_name: str, label: dict):
    global BOTS
    if bot_name in BOTS:
        return BOTS[bot_name]
    BOTS[bot_name] = FeishuBot(
        name=bot_name,
        webhook_url="https://open.feishu.cn/open-apis/bot/v2/hook/0b65bdf5-d869-49da-93ef-a82ada062b00",
        secret="zqBdbLAQibIg6rfzm6MIIb",
        label=label
    )
    return BOTS[bot_name]
