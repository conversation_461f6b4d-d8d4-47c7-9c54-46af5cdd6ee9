import os
import pandas as pd
import polars as pl
import numpy as np
from tqdm import tqdm
from scipy import stats
from typing import Union, List, Dict, Any
from datetime import datetime

from .common import BaseFactor
from .visu import BaseCanvas
from .parallel import MapReduce, get_param_dict

my_factor : BaseFactor = None
my_cv : BaseCanvas = BaseCanvas(name="Factor Analysis")

'''0 - base function'''
def init_factor(factor: BaseFactor):
    global my_factor
    my_factor = factor

def cal_factor(df: pl.DataFrame) -> pl.DataFrame:
    if my_factor is None:
        raise ValueError(f"unknown factor")
    return my_factor.cal(df)

def get_data(method: str, data:Union[str, pl.DataFrame] = None, param_li: list = None) -> pl.DataFrame:
    if method == "param_li":
        src = os.path.join(*param_li)
        try:
            file_path = f"{src}/{data}.csv"
            return pl.read_csv(file_path)
        except:
            file_path = f"{src}/{data}.parquet"
            return pl.read_parquet(file_path)
    else:
        raise ValueError(f"Unsupported get data format")

def remove_outliers_iqr(df: pl.DataFrame, colname: str) -> pl.DataFrame:
    # 计算 Q1 和 Q3
    q1 = df[colname].quantile(0.25)
    q3 = df[colname].quantile(0.75)
    iqr = q3 - q1
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    filtered_df = df.filter((df[colname] >= lower_bound) & (df[colname] <= upper_bound))
    return filtered_df

coins_df:pl.DataFrame = None
def get_symbol_list_by_date(date:str, symbol_suffix:str):
    global coins_df
    if coins_df == None:
        coins_df = pl.read_parquet("/data/token_insight/mkc_top100.parquet")
    find = coins_df.filter(pl.col("date") == date)
    if len(find) == 0:
        return []
    coins = find[0]["coins"].item().to_list()
    symbols = [coin + symbol_suffix for coin in coins]
    return symbols


'''1 - cal IC'''
def calculate_ic_and_rank_ic(df: pl.DataFrame, factor_col: str, price_col: str, periods: int = 1) -> pl.DataFrame:
    """
    计算 IC 和 Rank IC，并返回一个新的 DataFrame

    参数:
    df : pl.DataFrame
        输入的 DataFrame，包含因子和价格数据
    factor_col : str
        因子列名
    price_col : str
        价格列名
    periods : int
        预测期数，默认为1

    返回:
    pl.DataFrame : 包含 IC 和 Rank IC 的 DataFrame
    """
    forward_returns = df[price_col].pct_change(periods).shift(-periods)

    # filter nan
    mask = (df[factor_col].is_not_nan() & forward_returns.is_not_nan())
    factor = df[factor_col].filter(mask)
    forward_returns = forward_returns.filter(mask)

    if len(factor) == 0 or len(forward_returns) == 0:
        return pl.DataFrame({"IC": [None], "Rank IC": [None]})

    df_ic = pl.DataFrame({
        'factor': factor,
        'forward_returns': forward_returns
    })
    ic = df_ic.select(pl.corr('factor', 'forward_returns'))
    rank_ic = df_ic.select(pl.corr("factor", "forward_returns", method="spearman"))

    return pl.DataFrame({
        "IC": ic["factor"],
        "RankIC": rank_ic["factor"]
    })


def factor_ic(symbols: list, **kwargs):
    data_method = "param_li"
    param_li = kwargs["param_li"]
    results = []
    for symbol in symbols:
        df = get_data(data_method, symbol, param_li)
        df = df.with_columns(pl.col("candle_begin_time_GMT8").sort())
        df = cal_factor(df)
        ic_result = calculate_ic_and_rank_ic(df, "factor", 'close', periods=1)
        ic_result = ic_result.with_columns(pl.lit(symbol).alias("symbol"))
        ic_result = ic_result.select(['symbol', 'IC', 'RankIC'])
        results.append(ic_result)

    all_ic_results = pl.concat(results)
    # TODO: add option
    all_ic_results = remove_outliers_iqr(all_ic_results, "IC")
    all_ic_results = remove_outliers_iqr(all_ic_results, "RankIC")
    stats = all_ic_results.select([
        pl.col("IC").mean().alias("IC_Mean"),
        pl.col("RankIC").mean().alias("RankIC_Mean"),
        pl.col("IC").std().alias("IC_Std"),
        pl.col("RankIC").std().alias("RankIC_Std"),
        pl.col("IC").abs().min().alias("IC_abs_Min"),
        pl.col("RankIC").abs().min().alias("RankIC_abs_Min"),
        pl.col("IC").abs().max().alias("IC_abs_Max"),
        pl.col("RankIC").abs().max().alias("RankIC_abs_Max")
    ])
    my_cv.reset()
    my_cv.histogram(all_ic_results, "IC", "all ic")
    my_cv.histogram(all_ic_results, "RankIC", "all rank ic")
    my_cv.show()
    return all_ic_results, stats



'''2 - factor backtest'''
def factor_backtest_discarded(symbols: list,
                    trade_freq: int = 1,
                    groups_n: int = 10,
                    proc_n: int = 1,
                    **kwargs):
    data_method = "param_li"
    param_li = kwargs["param_li"]

    cnt = 0 # TODO RM
    results = []
    for symbol in symbols:
        df = get_data(data_method, symbol, param_li)
        df = df.with_columns(pl.col("candle_begin_time_GMT8").sort())
        df = cal_factor(df).drop_nulls()
        df = df.with_columns(pl.col("close").pct_change(trade_freq).shift(-trade_freq).alias("return")).drop_nulls() # get return
        df = df[["candle_begin_time_GMT8", "factor", "return"]]
        df = df.rename({"factor": f"factor_{symbol}", "return": f"return_{symbol}"})
        results.append(df)

        cnt += 1 # TODO RM
        if cnt == 10: break
    df = pl.concat(results, how="align").drop_nulls()

    # cut data for quick test
    df = df[:5000]

    def func_init(df:pl.DataFrame, n:int) -> Dict[str, Any]:
        return {"df": df, "n": n}

    def func_map(df:pl.DataFrame, n:int) -> List[pl.DataFrame]:
        size, remainder = (len(df) // n, len(df) % n)
        res = []
        start = 0
        for i in range(n):
            end = start + size + (1 if i < remainder else 0)
            res.append({"id":i, "df": df[start:end]})
            start = end
        return res

    def func_process(id:int, df:pl.DataFrame):
        results = []
        for time_group in df.group_by("candle_begin_time_GMT8"):
            time_value = time_group[0]
            group_df = time_group[1]
            # Extract factor columns and return columns
            factor_cols = [col for col in group_df.columns if col.startswith("factor_")]
            return_cols = [col for col in group_df.columns if col.startswith("return_")]

            factors_df = pl.DataFrame({
                "factor": group_df.select(factor_cols).transpose().to_series().to_list(),
                "return": group_df.select(return_cols).transpose().to_series().to_list()
            })

            assets_n = len(factors_df)
            factors_df = factors_df.with_columns(
                pl.col("factor").rank(method="min").alias("rank")
            ).with_columns(
                ((pl.col("rank") - 1) * groups_n / assets_n).cast(int).ceil().alias("decile")
            )

            # Calculate average return by decile
            decile_returns = factors_df.group_by("decile").agg(
                pl.col("return").mean().alias("avg_return"),
                pl.lit(time_value[0]).alias("candle_begin_time_GMT8")
            ).sort(by="decile")

            # Create a row with timestamp and returns by decile
            decile_pivot = decile_returns.pivot(
                index=None,
                columns="decile",
                values="avg_return"
            )
            results.append(decile_pivot)

        df = pl.concat(results).with_columns(
            (pl.col("^[0-9]$").name.prefix("ret_")),
            # (pl.col("^[0-9]$").add(1).cum_prod().name.prefix("cum_ret_")) # --> do it in reduce()
        )
        # df = df.with_columns(pl.int_range(len(df)).alias("t")) # --> do it in reduce()
        return {"id": id, "df":df}

    def func_reduce(results:list):
        li = [i["df"] for i in results]
        df = pl.concat(li)
        df = df.with_columns(pl.col("candle_begin_time_GMT8").sort())
        df = df.with_columns(
            (pl.col("^[0-9]$").add(1).cum_prod().name.prefix("cum_ret_")),
            pl.int_range(len(df)).alias("t")
        )
        return df

    mr:MapReduce = MapReduce(proc_n)
    mr.set_init_function(func_init, get_param_dict(func_init))
    mr.set_map_function(func_map, get_param_dict(func_map))
    mr.set_process_function(func_process, get_param_dict(func_process))
    mr.set_reduce_function(func_reduce, get_param_dict(func_reduce))
    df = mr.run({"df": df, "n":proc_n})

    print(f'backtest from {df[0]["candle_begin_time_GMT8"][0]} to {df[-1]["candle_begin_time_GMT8"][0]}')
    my_cv.reset(width=900, height=600)
    # my_cv.nline(df, "t", [f"cum_ret_{i}" for i in range(groups_n)], [f"cum_ret_{i}" for i in range(groups_n)])
    my_cv.nline(df, "candle_begin_time_GMT8", [f"cum_ret_{i}" for i in range(groups_n)], [f"cum_ret_{i}" for i in range(groups_n)])
    my_cv.show()



def factor_backtest(symbols: list,
                    trade_freq: int = 1,
                    groups_n: int = 10,
                    proc_n: int = 1,
                    **kwargs):

    if "start_time" in kwargs:
        start_dt = None
        if isinstance(kwargs["start_time"], str):
            try: start_dt = datetime.strptime(kwargs["start_time"], "%Y-%m-%d")
            except:
                raise ValueError(f'invalid {kwargs["start_time"]}, format be in "%Y-%m-%d"')

    if "end_time" in kwargs:
        end_dt = None
        if isinstance(kwargs["end_time"], str):
            try: end_dt = datetime.strptime(kwargs["end_time"], "%Y-%m-%d")
            except:
                raise ValueError(f'invalid {kwargs["end_time"]}, format be in "%Y-%m-%d"')

    if "symbol_suffix" not in kwargs: raise ValueError(f'symbol_suffix is missing')


    data_method = "param_li"
    param_li = kwargs["param_li"]
    results = []
    for symbol in symbols:
        df = get_data(data_method, symbol, param_li)
        df = df.with_columns(pl.col("candle_begin_time_GMT8").sort())
        df = cal_factor(df).drop_nulls()
        df = df.with_columns(pl.col("close").pct_change(trade_freq).shift(-trade_freq).alias("return")).drop_nulls() # get return
        df = df[["candle_begin_time_GMT8", "factor", "return"]]
        df = df.rename({"factor": f"factor_{symbol}", "return": f"return_{symbol}"})
        results.append(df)
    df = pl.concat(results, how="align")

    total = len(df.filter(pl.col("candle_begin_time_GMT8") <= end_dt)) if end_dt else len(df)
    pbar = tqdm(total=total, desc=f'backtest for factor', unit='frame')
    results = []
    for row in df.iter_rows(named=True):
        pbar.update(1)
        if row["candle_begin_time_GMT8"] < start_dt:continue
        if row["candle_begin_time_GMT8"] > end_dt:break

        date_str = row["candle_begin_time_GMT8"].strftime("%Y-%m-%d")
        symbols = get_symbol_list_by_date(date_str, kwargs["symbol_suffix"])
        if len(symbols) <= 0: continue
        cols = [col for col in df.columns if col.split("_", 1)[1] in symbols]
        cur_df = pl.DataFrame(row)[cols]
        # [TODO] still can have empty value since the symbol might be untradeable in this exchange, so you might need to drop_null
        cur_df = cur_df.select(pl.exclude([pl.Null]))
        # print(cur_df)

        group_df = cur_df
        # Extract factor columns and return columns
        factor_cols = [col for col in group_df.columns if col.startswith("factor_")]
        return_cols = [col for col in group_df.columns if col.startswith("return_")]

        factors_df = pl.DataFrame({
            "factor": group_df.select(factor_cols).transpose().to_series().to_list(),
            "return": group_df.select(return_cols).transpose().to_series().to_list()
        })

        assets_n = len(factors_df)
        factors_df = factors_df.with_columns(
            pl.col("factor").rank(method="ordinal").alias("rank")
        ).with_columns(
            ((pl.col("rank") - 1) * groups_n / assets_n).cast(int).ceil().alias("decile")
        )

        # Calculate average return by decile
        decile_returns = factors_df.group_by("decile").agg(
            pl.col("return").mean().alias("avg_return"),
            pl.lit(row["candle_begin_time_GMT8"]).alias("candle_begin_time_GMT8")
        ).sort(by="decile")

        # Create a row with timestamp and returns by decile
        decile_pivot = decile_returns.pivot(
            index=None,
            columns="decile",
            values="avg_return"
        )
        results.append(decile_pivot)
    pbar.close()

    df = pl.concat(results).with_columns(
            (pl.col("^[0-9]$").name.prefix("ret_")),
            (pl.col("^[0-9]$").add(1).cum_prod().name.prefix("cum_ret_"))
    )

    print(f'backtest from {df[0]["candle_begin_time_GMT8"][0]} to {df[-1]["candle_begin_time_GMT8"][0]}')
    my_cv.reset(width=900, height=600)
    my_cv.nline(df, "candle_begin_time_GMT8", [f"cum_ret_{i}" for i in range(groups_n)], [f"cum_ret_{i}" for i in range(groups_n)])
    my_cv.show()

