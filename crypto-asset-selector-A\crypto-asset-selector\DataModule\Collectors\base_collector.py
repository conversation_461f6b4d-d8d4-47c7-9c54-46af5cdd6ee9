import os
import time
import threading
from queue import Queue
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor

class BaseCollector:
    """
    API调用模板基类
    使用模板模式设计，提供通用的数据收集流程
    """
    def __init__(self,
                 workers=12,
                 max_retries=3,
                 api_limit=60,
                 output_path="./output"):
        """
        初始化BaseCollector

        参数:
        workers (int): 线程池工作线程数
        max_retries (int): 最大重试次数
        api_limit (int): API请求限制（每分钟）
        output_path (str): 输出数据存放目录
        """
        self.workers = workers
        self.max_retries = max_retries
        self.api_limit = api_limit
        self.output_path = output_path

        # 创建输出目录（如果不存在）
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path, exist_ok=True)

        # API请求限制
        self.request_times = []
        self.request_lock = threading.Lock()

        # 失败重试队列
        self.retry_queue = Queue()

        # 中间数据队列
        self.interval_queue = Queue()
        self.interval_data = []

        # 启动一个专门的线程处理间隔数据
        self.interval_processor = threading.Thread(target=self._process_interval_data)
        self.interval_processor.daemon = True
        self.interval_processor.start()

    def _process_interval_data(self):
        """处理间隔数据的专用线程"""
        while True:
            # 从队列中获取数据，这会阻塞直到队列中有数据
            item = self.interval_queue.get()

            # 特殊标记，表示处理完成
            if item is None:
                self.interval_queue.task_done()
                break

            self.interval_data.append(item)
            self.interval_queue.task_done()

    def check_and_update_rate_limit(self):
        """检查并更新API请求频率限制"""
        with self.request_lock:
            current_time = time.time()
            # 移除一分钟前的请求记录
            self.request_times = [t for t in self.request_times if current_time - t < 60]

            # 检查当前请求数是否达到限制
            if len(self.request_times) >= self.api_limit:
                # 计算需要等待的时间
                wait_time = 60 - (current_time - self.request_times[0])
                if wait_time > 0:
                    print(f"达到API限制，等待 {wait_time:.2f} 秒...")
                    time.sleep(wait_time)
                    # 清理过期的请求记录
                    self.request_times = [t for t in self.request_times if current_time + wait_time - t < 60]

            # 添加当前请求时间
            self.request_times.append(time.time())

    def process_retry_queue(self):
        """处理重试队列中的任务"""
        if self.retry_queue.empty():
            return 0

        print("开始处理重试队列...")
        retry_count = 0

        # 处理重试队列中的所有任务
        while not self.retry_queue.empty():
            task = self.retry_queue.get()
            self.process_task(task)
            retry_count += 1

        return retry_count

    def get_tasks(self, params):
        """
        获取任务列表

        参数:
        params: 运行参数

        返回:
        list: 任务列表
        """
        raise NotImplementedError("子类必须实现get_tasks方法")

    def process_task(self, task):
        """
        处理单个任务

        参数:
        task: 任务参数，由子类定义

        返回:
        tuple: (bool, ...) 第一个元素表示是否成功，其余元素由子类定义
        """
        raise NotImplementedError("子类必须实现process_task方法")

    def save_interval_data(self):
        """保存中间数据"""
        # 发送结束信号给处理线程
        self.interval_queue.put(None)
        # 等待所有任务完成
        self.interval_queue.join()
        self.interval_processor.join()

        if not self.interval_data:
            print("没有中间数据可保存")
            return

        # 具体保存逻辑由子类实现
        self._save_interval_data_impl()

    def _save_interval_data_impl(self):
        """保存中间数据的具体实现，由子类提供"""
        raise NotImplementedError("子类必须实现_save_interval_data_impl方法")

    def run(self, params):
        """
        运行数据收集流程

        参数:
        params: 运行参数，由子类定义

        返回:
        由子类定义的结果
        """
        # 模板方法模式
        self.pre_handle(params)
        result = self.handle(params)
        return self.post_handle(result)

    def pre_handle(self, params):
        """
        预处理阶段，在主处理前执行

        参数:
        params: 运行参数
        """
        # 默认实现或钩子
        pass

    def handle(self, params):
        """
        主处理阶段，执行核心业务逻辑

        参数:
        params: 运行参数

        返回:
        处理结果
        """
        # 获取任务列表
        tasks = self.get_tasks(params)

        if not tasks:
            print("没有任务需要处理")
            return None

        print(f"开始处理 {len(tasks)} 个任务")

        # 使用线程池处理每个任务
        successful = 0
        failed = 0

        start_time = time.time()

        with ThreadPoolExecutor(max_workers=self.workers) as executor:
            futures = [executor.submit(self.process_task, task) for task in tasks]

            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    if result and result[0]:  # 假设返回的第一个元素表示成功与否
                        successful += 1
                    else:
                        failed += 1
                except Exception as e:
                    failed += 1
                    print(f"处理任务时出错: {e}")

        # 处理重试队列
        retry_count = 0
        while not self.retry_queue.empty():
            retry_processed = self.process_retry_queue()
            retry_count += retry_processed
            if retry_processed == 0:
                break

        end_time = time.time()

        # 保存中间数据
        self.save_interval_data()

        result = {
            "successful": successful,
            "failed": failed,
            "total": len(tasks),
            "retry_count": retry_count,
            "time_elapsed": end_time - start_time
        }

        print(f"任务完成. 总计: {result['total']}, 成功: {result['successful']}, 失败: {result['failed']}, 重试: {result['retry_count']}")
        print(f"耗时: {result['time_elapsed']:.2f} 秒")

        return result

    def post_handle(self, result):
        """
        后处理阶段，在主处理后执行

        参数:
        result: 主处理阶段的结果

        返回:
        最终处理结果
        """
        # 默认实现或钩子
        return result
