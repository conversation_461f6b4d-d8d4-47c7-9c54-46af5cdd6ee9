import os
import polars as pl
import time
import requests
import concurrent.futures
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor

from .base_collector import BaseCollector
from ..Utils.env_loader import TOKEN_INSIGHT_GET_COIN_HISTORY, get_api_headers

class TokenInsightCoinHistoryCollector(BaseCollector):
    """TokenInsight币种历史数据收集器"""

    def fetch_coin_history(self, coin_id, interval="day", length=-1, vs_currency="usd"):
        """
        获取币种历史数据

        参数:
        coin_id (str): 币种ID
        interval (str): 时间间隔，可选值为minute、hour、day，默认为day
        length (int): 返回的数据长度，-1表示最大值，默认为-1
        vs_currency (str): 计价货币，默认为usd

        返回:
        tuple: (polars.DataFrame, str, int, int, float)
               币种历史数据DataFrame、符号、首个时间戳、最后时间戳、当前市值
        """
        # 使用环境变量中的API URL
        url = f"{TOKEN_INSIGHT_GET_COIN_HISTORY}{coin_id}"
        params = {
            "interval": interval,
            "length": length,
            "vs_currency": vs_currency
        }

        # 获取包含API KEY的headers
        headers = get_api_headers()

        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data["status"]["code"] == 0:
                history_data = data["data"]["market_chart"]
                symbol = data["data"]["symbol"]

                if not history_data:
                    print(f"警告: {coin_id} 没有历史数据")
                    return None, symbol, None, None, None

                # 提取所需列
                df = pl.DataFrame([{
                    "price": item.get("price"),
                    "vol_spot_24h": item.get("vol_spot_24h"),
                    "market_cap": item.get("market_cap"),
                    "timestamp": item.get("timestamp")
                } for item in history_data])

                # 获取首个和最后时间戳
                timestamps = sorted([item.get("timestamp") for item in history_data])
                first_timestamp = timestamps[0] if timestamps else None
                last_timestamp = timestamps[-1] if timestamps else None

                # 获取最新的市值数据
                latest_data = sorted(history_data, key=lambda x: x.get("timestamp", 0), reverse=True)[0]
                current_market_cap = latest_data.get("market_cap")

                return df, symbol, first_timestamp, last_timestamp, current_market_cap
            else:
                raise Exception(f"API调用错误: {data['status']['message']}")

        except Exception as e:
            print(f"获取 {coin_id} 历史数据时出错: {e}")
            return None, None, None, None, None

    def save_to_parquet(self, df, symbol, coin_id, first_timestamp, last_timestamp, output_dir="./"):
        """
        将DataFrame保存为parquet文件

        参数:
        df (polars.DataFrame): 要保存的DataFrame
        symbol (str): 币种符号
        coin_id (str): 币种ID
        first_timestamp (int): 首个时间戳
        last_timestamp (int): 最后时间戳
        output_dir (str): 输出目录路径

        返回:
        str: 输出文件路径
        """
        try:
            # 确保输出目录存在
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            filename = f"{symbol}_{coin_id}_history_{first_timestamp}_{last_timestamp}.parquet"
            output_path = os.path.join(output_dir, filename)

            df.write_parquet(output_path)
            print(f"数据已保存到 {output_path}")
            return output_path
        except Exception as e:
            print(f"保存 {symbol}_{coin_id} 数据时出错: {e}")
            return None

    def __init__(self,
                 workers=12,
                 max_retries=3,
                 api_limit=60,
                 output_path="./output",
                 interval_output_path="collected_coins_interval.parquet",
                 interval="day",
                 length=-1,
                 vs_currency="usd"):
        """
        初始化TokenInsightCoinHistoryCollector

        参数:
        workers (int): 线程池工作线程数
        max_retries (int): 最大重试次数
        api_limit (int): API请求限制（每分钟）
        output_path (str): 输出数据存放目录
        interval_output_path (str): 间隔数据输出文件路径
        interval (str): 时间间隔，可选值为minute、hour、day
        length (int): 返回的数据长度，-1表示最大值
        vs_currency (str): 计价货币
        """
        super().__init__(workers, max_retries, api_limit, output_path)
        self.interval_output_path = interval_output_path
        self.interval = interval
        self.length = length
        self.vs_currency = vs_currency
        self.coins_data = None  # 存储币种列表数据

    def pre_handle(self, params):
        """
        预处理阶段，获取币种列表数据

        参数:
        params (dict): 运行参数，必须包含coins_data
        """
        # 更新参数（如果提供）
        if params:
            if 'interval' in params:
                self.interval = params['interval']
            if 'length' in params:
                self.length = params['length']
            if 'vs_currency' in params:
                self.vs_currency = params['vs_currency']
            if 'interval_output_path' in params:
                self.interval_output_path = params['interval_output_path']

            # 获取币种列表数据
            if 'coins_data' in params:
                self.coins_data = params['coins_data']
            else:
                raise ValueError("必须提供coins_data参数")

    def get_tasks(self, params):
        """
        获取任务列表

        参数:
        params (dict): 运行参数

        返回:
        list: 任务列表，每个任务是一个(coin_id, symbol)元组
        """
        if self.coins_data is None or len(self.coins_data) == 0:
            print("未提供币种列表数据")
            return []

        print(f"开始处理 {len(self.coins_data)} 个币种的历史数据")

        # 创建任务列表
        tasks = [(coin["id"], coin["symbol"]) for coin in self.coins_data]
        return tasks

    def handle(self, params):
        """
        主处理阶段，执行核心业务逻辑

        参数:
        params (dict): 运行参数

        返回:
        处理结果
        """
        # 调用父类的handle方法，使用并发处理任务
        return super().handle(params)

    def post_handle(self, result):
        """
        后处理阶段，处理结果统计

        参数:
        result (dict): 处理结果统计

        返回:
        dict: 处理结果统计
        """
        return result

    def process_task(self, task):
        """
        处理单个任务

        参数:
        task: 任务参数，(coin_id, symbol)元组

        返回:
        tuple: (bool, str, str) 是否成功、币种符号、币种ID
        """
        coin_id, symbol = task
        retry_count = 0

        # 如果是重试任务
        if len(task) == 3:
            coin_id, symbol, retry_count = task

        print(f"正在获取 {symbol}({coin_id}) 的历史数据..." + (f" 重试 #{retry_count}" if retry_count > 0 else ""))

        # 检查并遵守API请求限制
        self.check_and_update_rate_limit()

        df, symbol, first_timestamp, last_timestamp, current_market_cap = self.fetch_coin_history(
            coin_id, self.interval, self.length, self.vs_currency
        )

        if df is not None and not df.is_empty():
            # 构建输出路径
            output_file = f"{symbol}_{coin_id}_history_{first_timestamp}_{last_timestamp}.parquet"
            full_output_path = os.path.join(self.output_path, output_file)

            # 保存历史数据
            self.save_to_parquet(df, symbol, coin_id, first_timestamp, last_timestamp, self.output_path)

            # 使用队列安全地更新间隔数据，无需显式锁
            self.interval_queue.put({
                "symbol": symbol,
                "id": coin_id,
                "first_timestamp": first_timestamp,
                "last_timestamp": last_timestamp,
                "current_market_cap": current_market_cap,
                "file_path": full_output_path
            })

            return True, symbol, coin_id
        else:
            # 添加到重试队列
            if retry_count < self.max_retries:
                print(f"获取 {symbol}({coin_id}) 数据失败，添加到重试队列")
                self.retry_queue.put((coin_id, symbol, retry_count + 1))
            else:
                print(f"获取 {symbol}({coin_id}) 数据失败，已达最大重试次数")

        return False, symbol, coin_id



    def _save_interval_data_impl(self):
        """保存中间数据的具体实现"""
        if not self.interval_data:
            print("没有间隔数据可保存")
            return

        df = pl.DataFrame(self.interval_data)
        full_interval_path = os.path.join(self.output_path, self.interval_output_path)
        df.write_parquet(full_interval_path)
        print(f"币种时间间隔数据已保存到 {full_interval_path}")
        print(f"收集了 {len(self.interval_data)} 个币种的时间间隔数据")
