import os
import sys
import yaml
import argparse
from typing import Dict, Any

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DataModule.Collectors.collector_registry import registry
from DataModule.Utils.env_loader import TOKEN_INSIGHT_API_LIMIT

def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    参数:
    config_path (str): 配置文件路径
    
    返回:
    Dict: 配置信息
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 替换环境变量
        config_str = str(config)
        for env_var in ["TOKEN_INSIGHT_API_KEY", "TOKEN_INSIGHT_API_LIMIT"]:
            env_value = os.environ.get(env_var) or ""
            config_str = config_str.replace(f"${{{env_var}}}", env_value)
        
        # 转回字典
        import ast
        config = ast.literal_eval(config_str)
        
        return config
    except Exception as e:
        print(f"加载配置文件出错: {e}")
        return {}

def run_data_collection(config: Dict[str, Any], args: argparse.Namespace) -> None:
    """
    运行数据收集
    
    参数:
    config (Dict): 配置信息
    args (argparse.Namespace): 命令行参数
    """
    # 注册收集器
    registry.register(config)
    
    # 获取全局设置
    settings = config.get("Settings", {})
    
    # 合并命令行参数和配置文件设置
    global_settings = {
        "Workers": args.workers or settings.get("Workers", 12),
        "MaxRetries": args.max_retries or settings.get("MaxRetries", 3),
        "CachePath": settings.get("CachePath", {
            "CoinList": "DataModule/Cache/collected_coins.parquet",
            "IntervalData": "DataModule/Cache/collected_coins_interval.parquet"
        }),
        "OutputPath": args.output_path or settings.get("OutputPath", "DataModule/Cache/coins_history"),
        "SymbolListPath": args.symbol_list or settings.get("SymbolListPath", "symbol_list.csv"),
        "VSCurrency": args.vs_currency or settings.get("VSCurrency", "usd"),
        "Interval": args.interval or settings.get("Interval", "day"),
        "Length": args.length if args.length is not None else settings.get("Length", -1),
        "CoinPool": args.coin_pool or settings.get("CoinPool", 15000),
        "FetchLimit": args.fetch_limit or settings.get("FetchLimit", 1500),
        "UseCache": not args.no_cache if hasattr(args, "no_cache") and args.no_cache is not None else settings.get("UseCache", True)
    }
    
    # 执行收集器流水线
    print("开始执行数据收集流水线...")
    results = registry.execute_pipeline(global_settings)
    
    # 输出执行结果
    print("\n执行结果汇总:")
    for collector_key, result in results.items():
        success = result.get("success", False)
        message = result.get("message", "")
        status = "成功" if success else "失败"
        print(f"  {collector_key}: {status} - {message}")
    
    # 获取最后一个执行的收集器的结果
    if registry._execution_flow:
        last_collector = registry._execution_flow[-1]
        last_result = results.get(last_collector, {})
        if last_result.get("success", False) and "data" in last_result:
            data = last_result.get("data", {})
            if isinstance(data, dict):  # 如果是历史数据收集器的结果
                print(f"\n数据收集完成，总计: {data.get('total', 0)}, 成功: {data.get('successful', 0)}")
                print(f"输出目录: {data.get('output_path', '')}")
                print(f"间隔数据: {data.get('interval_data_path', '')}")

def main():
    parser = argparse.ArgumentParser(description="加密货币数据收集")
    parser.add_argument("--config", type=str, default="DataModule/data_config.yaml", help="配置文件路径")
    parser.add_argument("--workers", type=int, help="线程池工作线程数")
    parser.add_argument("--max_retries", type=int, help="最大重试次数")
    parser.add_argument("--output_path", type=str, help="输出目录路径")
    parser.add_argument("--symbol_list", type=str, help="符号列表文件路径")
    parser.add_argument("--vs_currency", type=str, help="计价货币")
    parser.add_argument("--interval", type=str, choices=["minute", "hour", "day"], help="时间间隔")
    parser.add_argument("--length", type=int, help="数据长度，-1表示全部")
    parser.add_argument("--coin_pool", type=int, help="要获取的最大币种数量")
    parser.add_argument("--fetch_limit", type=int, help="每次API请求获取的币种数量")
    parser.add_argument("--no_cache", action="store_true", help="不使用缓存")
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    if not config:
        print("加载配置失败，退出程序")
        return
    
    # 运行数据收集
    run_data_collection(config, args)

if __name__ == "__main__":
    main() 