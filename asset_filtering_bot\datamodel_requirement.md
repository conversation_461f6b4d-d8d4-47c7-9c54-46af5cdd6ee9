## 1. 任务概述

我想构建一个资产筛选机器人，它由以下几个部分组成：

1. 数据获取模块
2. 筛选引擎
3. 图形化界面展示

本期的任务是构建数据获取模块的框架。简单的来说，就是重构一下coin_mkc_collector.py，解耦其与coin_list_fetcher.py与coin_history_fetcher.py的硬编码关系，并将其改造成对拓展开放，对修改关闭的框架。

## 2. 数据获取模块结构

数据获取模块由以下几个部分组成：

1. 数据源API：可参考coin_list_fetcher.py与coin_history_fetcher.py，基本上可以理解成一个方法，给定输入，获取想要的输出。
2. 连接池：参考coin_mkc_collector.py中多线程的写法，并行调用数据源API。
3. 数据库：以某种形式储存获得的parquet数据，可以是直接存储或者使用中间件。


## 3. 连接池要求

连接池需要满足以下要求：

1. 抽象化：连接池需要抽象化数据源API的调用，并提供统一的接口，同时支持prehandle,handle,posthandle等钩子函数。
2. 支持并发：连接池需要支持并发调用数据源API，并且尽量少使用硬加锁，可以考虑使用队列等机制解决可能存在的竞争问题。
3. 可配置：连接池需要支持配置化，支持配置化主要体现在支持配置化数据源API的调用参数，以及支持配置化钩子函数。
4. 对不同的API支持使用不同的调用策略：既能支持并发，也能支持串行。


## 4. 数据库要求

数据库组件尚未确定选型（如使用中间件类似mysql或redis）还是直接以parquet文件存储，所以仅给出以下要求：

1. 获取的数据在内存中会以polars的DataFrame形式存在，最好支持列式存储如parquet。
2. 之后其他的模块（如筛选引擎）会从数据获取模块获得的数据中取数，所以需要支持数据表的查询。

请你根据以上要求提出建议。

## 5. 总结

请你根据以上要求，产出以下结果：
1. 给出数据获取模块的框架设计。
2. 对数据库选型提出建议。
3. 给出连接池的详细涉及，包括需求文档、涉及的类、ER关系、UML流程、接口设计。

你不允许修改coin_mkc_collector.py，也不允许修改coin_list_fetcher.py与coin_history_fetcher.py。你目前不需要设计筛选引擎与图形化界面展示。
