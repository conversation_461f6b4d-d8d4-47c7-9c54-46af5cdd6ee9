import sys
import polars as pl
sys.path.append("/data/code")

from quant import cal_factor_ic, BaseFactor, init_factor
# from quant.factor_analysis import cal_factor_ic, BaseFactor, init_factor

class MyFactor(BaseFactor):

    def f_zhenfu(self, df: pl.DataFrame):
        return df.with_columns((df["high"] / df["low"] - 1).alias("factor"))

    def f_vol_ratio(self, df: pl.DataFrame):
        try:
            # return df.with_columns((df["volume"] / df["volume"].rolling_mean(5, [5,4,3,2,1])).alias("factor"))
            return df.with_columns((df["volume"] / df["volume"].rolling_mean(5)).alias("factor"))
        except:
            # return df.with_columns((df["vol"] / df["vol"].rolling_mean(5, [5,4,3,2,1])).alias("factor"))
            return df.with_columns((df["vol"] / df["vol"].rolling_mean(5)).alias("factor"))

    def f_range(self, df: pl.DataFrame):
        return df.with_columns(((df["high"] - df["low"]) / df["open"]).alias("factor"))

    def ff_volatility(self, df: pl.DataFrame):
        df = self.f_range(df).rename({"factor": "range"})
        return df.with_columns((df["range"].rolling_std(window_size=5, min_periods=1)).alias("factor"))

    def cal(self, df: pl.DataFrame):
        return self.f_range(df)


init_factor(MyFactor())

path_param = ["/", "data", "kline_okx", "perp", "1h"]
all_ic_results, detailed_stats = cal_factor_ic(symbols=['BTC_USDT', 'TRUMP_USDT'], data_method = "param_li", param_li = path_param)
print("\nIC值:")
print(all_ic_results)
print("\n详细统计信息:")
print(detailed_stats)