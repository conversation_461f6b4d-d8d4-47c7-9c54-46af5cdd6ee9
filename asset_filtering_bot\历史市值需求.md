## 一、任务概述

在coins_mkc_history中有一些加密货币的历史市值数据，我需要你使用polars库对这些数据进行处理，得到可得范围内的每一天的topK（从大到小排序前K）市值的资产的数据。

## 二、输入要求

1. coins_mkc_history目录下有多个parquet文件，每个文件代表一个加密货币的历史市值数据，文件名格式为{symbol}_{id}_history_{first_timestamp}_{last_timestamp}.parquet，其中symbol是加密货币的符号，id是加密货币的唯一标识符，first_timestamp和last_timestamp是数据的起始和结束时间戳。

2. collected_coins_interval.parquet文件包含所有加密货币的时间范围信息，包括symbol, id, first_timestamp, last_timestamp, current_market_cap。

3. 每一个加密货币的历史市值数据文件包含price, vol_spot_24h, market_cap, timestamp四个列。

## 三、输出要求

我希望你使用polars对数据进行处理，得到以下格式的输出：

```
{{date1:[{asset1:market cap1}, {asset2:market cap2}...{assetK:market capK}]}, {date2:[{asset1:market cap1}, {asset2:market cap2}...{assetK:market capK}]}}
```

即，输出一个parquet文件，其中一列为时间戳timestamp，代表历史上的某个时刻。另一列为输出列表 topK,内容是一个list，list中包含K个asset的symbol、asset在对应时间戳的市值market cap，list中的元素按照market cap从大到小排序。

## 四、程序要求

1. 使用python编写，使用polars库，允许一次性将所有parquet文件读入内存，尽量避免频繁IO。尽量利用polars库的科学计算特性做运算。留好参数以控制K（K默认为100）、输出路径。

2. 请注意，asset的生命周期各不相同，由于各asset出现、消失的时间段不同，可能存在某些时间点/段无法凑齐topK个asset。对于这种情况，请你设置一个bool参数，用于控制是否保留这些时间点。如果保留，则这些时间点的list输出已排序的所有可输出的asset的数据，否则直接丢弃这些时间点。