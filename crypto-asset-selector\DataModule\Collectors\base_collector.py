import concurrent.futures
import threading
import time
from queue import Queue
from typing import Dict, Any, List, Optional, Tuple

class BaseCollector:
    """基础收集器类，使用模板方法模式"""
    
    def __init__(self, workers: int = 12, max_retries: int = 3, api_limit: int = 60, api_key: str = None):
        """
        初始化收集器
        
        参数:
        workers (int): 线程池工作线程数
        max_retries (int): 最大重试次数
        api_limit (int): API每分钟请求数限制
        """
        self.workers = workers
        self.max_retries = max_retries
        self.api_limit = api_limit
        self.api_key = api_key
        
        # 线程池
        self.executor = None
        
        # API请求限制
        self.request_times = []
        self.request_lock = threading.Lock()
        
        # 失败重试队列
        self.retry_queue = Queue()
        
        # 数据处理队列
        self.data_queue = Queue()
        self.collected_data = []
        
        # 启动数据处理线程
        self.data_processor = threading.Thread(target=self._process_data)
        self.data_processor.daemon = True
        self.data_processor.start()
    
    def run(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        收集器执行的模板方法
        
        参数:
        params (Dict): 执行参数
        
        返回:
        Dict: 处理结果
        """
        # 模板方法模式的主要步骤
        prepared_params = self.pre_handle(params)
        result = self.handle(prepared_params)
        final_result = self.post_handle(result)
        
        # 等待数据处理完成
        self.wait_for_completion()
        
        return final_result
    
    def pre_handle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理参数，可被子类重写
        
        参数:
        params (Dict): 原始参数
        
        返回:
        Dict: 预处理后的参数
        """
        return params
    
    def handle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        核心处理逻辑，必须由子类实现
        
        参数:
        params (Dict): 预处理后的参数
        
        返回:
        Dict: 处理结果
        """
        raise NotImplementedError("子类必须实现handle方法")
    
    def post_handle(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        后处理结果，可被子类重写
        
        参数:
        result (Dict): 处理结果
        
        返回:
        Dict: 后处理后的结果
        """
        return result
    
    def execute_with_threadpool(self, task_func, task_items: List[Dict[str, Any]]) -> List[Any]:
        """
        使用线程池执行多个任务
        
        参数:
        task_func: 任务函数
        task_items: 任务项列表
        
        返回:
        List: 执行结果列表
        """
        results = []
        successful = 0
        failed = 0
        
        # 创建线程池
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.workers) as self.executor:
            # 提交所有任务
            futures = [self.executor.submit(task_func, **item) for item in task_items]
            
            # 处理完成的任务
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    if result and result.get('success', False):
                        successful += 1
                    else:
                        failed += 1
                    results.append(result)
                except Exception as e:
                    failed += 1
                    print(f"任务执行出错: {e}")
        
        # 处理重试队列
        retry_count = 0
        while not self.retry_queue.empty():
            retry_processed = self.process_retry_queue(task_func)
            retry_count += retry_processed
            if retry_processed == 0:
                break
        
        print(f"任务完成. 总计: {len(task_items)}, 成功: {successful}, 失败: {failed}, 重试: {retry_count}")
        return results
    
    def process_retry_queue(self, task_func) -> int:
        """
        处理重试队列
        
        参数:
        task_func: 任务函数
        
        返回:
        int: 处理的任务数
        """
        if self.retry_queue.empty():
            return 0
        
        print("开始处理重试队列...")
        retry_count = 0
        
        # 处理重试队列中的所有任务
        while not self.retry_queue.empty():
            task_params = self.retry_queue.get()
            retry_num = task_params.pop('retry_count', 0)
            task_params['retry_count'] = retry_num + 1
            
            try:
                result = task_func(**task_params)
                if not result or not result.get('success', False):
                    # 如果未达到最大重试次数，则再次加入重试队列
                    if retry_num + 1 < self.max_retries:
                        self.retry_queue.put(task_params)
            except Exception as e:
                print(f"重试任务出错: {e}")
                # 如果未达到最大重试次数，则再次加入重试队列
                if retry_num + 1 < self.max_retries:
                    self.retry_queue.put(task_params)
            
            retry_count += 1
        
        return retry_count
    
    def check_and_update_rate_limit(self):
        """检查并更新API请求频率限制"""
        with self.request_lock:
            current_time = time.time()
            # 移除一分钟前的请求记录
            self.request_times = [t for t in self.request_times if current_time - t < 60]
            
            # 检查当前请求数是否达到限制
            if len(self.request_times) >= self.api_limit:
                # 计算需要等待的时间
                wait_time = 60 - (current_time - self.request_times[0])
                if wait_time > 0:
                    print(f"达到API限制，等待 {wait_time:.2f} 秒...")
                    time.sleep(wait_time)
                    # 清理过期的请求记录
                    self.request_times = [t for t in self.request_times if current_time + wait_time - t < 60]
            
            # 添加当前请求时间
            self.request_times.append(time.time())
    
    def _process_data(self):
        """处理数据的专用线程"""
        while True:
            # 从队列中获取数据，这会阻塞直到队列中有数据
            item = self.data_queue.get()
            
            # 特殊标记，表示处理完成
            if item is None:
                self.data_queue.task_done()
                break
            
            self.collected_data.append(item)
            self.data_queue.task_done()
    
    def add_to_data_queue(self, item: Dict[str, Any]):
        """
        向数据处理队列添加项
        
        参数:
        item (Dict): 数据项
        """
        self.data_queue.put(item)
    
    def wait_for_completion(self):
        """等待所有数据处理完成"""
        # 发送结束信号给处理线程
        self.data_queue.put(None)
        # 等待所有任务完成
        self.data_queue.join()
        self.data_processor.join() 