import asyncio
import random
import logging
from retry_queue import RetryQueue

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockTask:
    """模拟任务类，用于测试重试队列"""
    
    def __init__(self, success_rate: float = 0.3):
        """
        初始化模拟任务
        
        参数:
        success_rate (float): 成功率，0.0-1.0之间
        """
        self.success_rate = success_rate
        self.attempt_count = 0
    
    async def simulate_api_call(self, task_id: str, data: str) -> tuple[bool, dict]:
        """
        模拟API调用
        
        参数:
        task_id (str): 任务ID
        data (str): 任务数据
        
        返回:
        tuple[bool, dict]: (是否成功, 结果数据)
        """
        self.attempt_count += 1
        
        # 模拟网络延迟
        await asyncio.sleep(random.uniform(0.1, 0.5))
        
        # 随机决定是否成功
        success = random.random() < self.success_rate
        
        if success:
            logger.info(f"任务 {task_id} 成功，数据: {data}")
            return True, {"task_id": task_id, "data": data, "attempts": self.attempt_count}
        else:
            logger.warning(f"任务 {task_id} 失败，数据: {data}")
            return False, {"task_id": task_id, "data": data, "attempts": self.attempt_count}

async def test_retry_queue():
    """测试重试队列功能"""
    logger.info("开始测试重试队列")
    
    # 创建重试队列
    retry_queue = RetryQueue(
        max_retries=3,
        retry_delay=0.2,
        max_concurrent_retries=3,
        enable_exponential_backoff=True
    )
    
    # 创建模拟任务
    mock_task = MockTask(success_rate=0.4)  # 40% 成功率
    
    # 启动后台重试处理器
    retry_processor = await retry_queue.start_background_processor()
    
    # 创建一些初始失败的任务
    failed_tasks = []
    for i in range(5):
        task_id = f"task_{i}"
        data = f"data_{i}"
        
        # 模拟首次尝试失败
        success, result = await mock_task.simulate_api_call(task_id, data)
        
        if not success:
            # 添加到重试队列
            await retry_queue.add_task(
                task_func=mock_task.simulate_api_call,
                task_args=(task_id, data),
                task_id=task_id,
                metadata={"original_data": data}
            )
            failed_tasks.append(task_id)
    
    logger.info(f"添加了 {len(failed_tasks)} 个失败任务到重试队列")
    
    # 等待重试队列处理完毕
    await retry_queue.wait_for_completion()
    
    # 停止后台处理器
    retry_processor.cancel()
    
    # 获取统计信息
    stats = retry_queue.get_stats()
    logger.info(f"重试队列统计: {stats}")
    
    logger.info("重试队列测试完成")

async def test_concurrent_retries():
    """测试并发重试功能"""
    logger.info("开始测试并发重试")
    
    # 创建重试队列
    retry_queue = RetryQueue(
        max_retries=2,
        retry_delay=0.1,
        max_concurrent_retries=2,
        enable_exponential_backoff=False
    )
    
    # 创建模拟任务
    mock_task = MockTask(success_rate=0.2)  # 20% 成功率
    
    # 同时添加多个失败任务
    tasks = []
    for i in range(10):
        task_id = f"concurrent_task_{i}"
        data = f"concurrent_data_{i}"
        
        # 直接添加到重试队列（模拟首次失败）
        await retry_queue.add_task(
            task_func=mock_task.simulate_api_call,
            task_args=(task_id, data),
            task_id=task_id,
            metadata={"test_type": "concurrent"}
        )
    
    # 处理重试队列
    await retry_queue.process_retry_queue()
    
    # 获取统计信息
    stats = retry_queue.get_stats()
    logger.info(f"并发重试统计: {stats}")
    
    logger.info("并发重试测试完成")

async def main():
    """主测试函数"""
    logger.info("开始RetryQueue测试")
    
    # 测试基本重试功能
    await test_retry_queue()
    
    print("\n" + "="*50 + "\n")
    
    # 测试并发重试功能
    await test_concurrent_retries()
    
    logger.info("所有测试完成")

if __name__ == "__main__":
    asyncio.run(main())
