# 加密货币数据收集工具

该工具用于从TokenInsight API获取加密货币的列表和历史数据。工具会根据本地的symbol_list.csv过滤币种，并获取这些币种的历史数据。

## 功能特点

- 支持获取所有可用币种列表并过滤
- **支持批量获取最多约16000个币种数据**
- 支持获取各币种的历史价格数据
- 支持多线程并发获取数据，提高效率
- 使用阻塞队列安全处理数据，避免竞争
- 完整记录每个币种的数据时间范围和当前市值
- 数据以Parquet格式保存，便于后续分析
- 从.env文件加载API配置，避免硬编码

## 环境配置

在项目根目录创建一个.env文件，包含以下内容：

```
TOKEN_INSIGHT_GET_COIN_HISTORY="https://api.tokeninsight.com/api/v1/history/coins/"
TOKEN_INSIGHT_GET_COIN_LIST="https://api.tokeninsight.com/api/v1/coins/list"
TOKEN_INSIGHT_API_KEY="你的API密钥"
TOKEN_INSIGHT_API_LIMIT=60 (免费API60次/分，付费API另说)
```

请确保替换为你自己的TokenInsight API密钥。

## 文件结构

- `env_loader.py`：环境变量加载模块
- `coin_list_fetcher.py`：获取币种列表的模块
- `coin_history_fetcher.py`：获取币种历史数据的模块
- `coin_mkc_collector.py`：主程序，协调整个数据获取过程
- `symbol_list.csv`：需要过滤的币种符号列表(运行get_symbols.py获得)

## 使用方法

### 独立运行币种列表获取模块

```bash
python coin_list_fetcher.py --limit 1500 --offset 0 --vs_currency usd --output collected_coins.parquet --filter --symbol_list symbol_list.csv
```

参数说明：
- `--limit`：返回的币种数量，默认1500，最大1500
- `--offset`：偏移量，默认0
- `--vs_currency`：计价货币，默认为usd
- `--output`：输出文件路径，默认为collected_coins.parquet
- `--filter`：是否使用symbol_list.csv过滤币种
- `--symbol_list`：symbol列表文件路径，默认为symbol_list.csv

### 独立运行币种历史数据获取模块

```bash
python coin_history_fetcher.py --coin_id bitcoin --interval day --length -1 --vs_currency usd
```

参数说明：
- `--coin_id`：币种ID（必填）
- `--interval`：时间间隔，可选值为minute、hour、day，默认为day
- `--length`：返回的数据长度，-1表示最大值，默认为-1
- `--vs_currency`：计价货币，默认为usd

### 运行主程序

```bash
python coin_mkc_collector.py --workers 2 --coins_cache collected_coins.parquet --symbol_list symbol_list.csv --interval_output collected_coins_interval.parquet --output_path ./coins_mkc_history --coin_pool 15704 --fetch_limit 1500 --vs_currency usd
```

参数说明：
- `--workers`：线程池工作线程数，默认为2
- `--coins_cache`：币种缓存文件路径，默认为collected_coins.parquet
- `--symbol_list`：符号列表文件路径，默认为symbol_list.csv
- `--interval_output`：间隔数据输出文件路径，默认为collected_coins_interval.parquet
- `--output_path`：输出数据存放目录，默认为./coins_mkc_history
- `--coin_pool`：要获取的最大币种数量，默认为15704，最大约16000
- `--fetch_limit`：每次API请求获取的币种数量，默认为1500，最大1500
- `--vs_currency`：计价货币，默认为usd

## 批量获取币种数据

工具现在支持批量获取大量币种数据。通过设置 `coin_pool` 参数，你可以控制要获取的最大币种数量，最多可达约16000个：

```bash
# 获取约5000个币种数据
python coin_mkc_collector.py --coin_pool 5000

# 获取约10000个币种数据
python coin_mkc_collector.py --coin_pool 10000

# 获取最大数量的币种数据（约16000个）
python coin_mkc_collector.py --coin_pool 16000
```

程序会自动分批次获取数据，每批次最多1500个币种（可通过 `fetch_limit` 参数调整），同时遵守API请求频率限制，避免触发限流。

## 输出文件

1. `collected_coins.parquet`：包含过滤后的币种列表，字段包括price, symbol, id, spot_volume_24h, price_change_percentage_24h
2. `{symbol}_{id}_history_{first_timestamp}_{last_timestamp}.parquet`：每个币种的历史数据，字段包括price, vol_spot_24h, market_cap, timestamp
3. `collected_coins_interval.parquet`：所有币种的时间范围信息，包括symbol, id, first_timestamp, last_timestamp, current_market_cap

## 依赖库

- requests：用于API调用
- polars：数据处理和Parquet文件读写
- concurrent.futures：多线程处理
- python-dotenv：环境变量加载

## 注意事项

1. 确保网络连接正常，可以访问TokenInsight API
2. 确保已设置正确的API密钥在.env文件中
3. 如果本地已存在collected_coins.parquet文件，程序将优先使用该文件，避免重复API调用
4. 多线程获取数据时，请根据自己的网络环境和机器性能适当调整workers参数
5. 获取大量币种数据时，请注意API调用限制和本地磁盘空间

## 可能的错误和解决方案

1. API请求失败：检查API密钥是否正确，网络连接是否正常
2. 文件保存失败：检查磁盘空间和权限
3. 空数据：某些币种可能没有历史数据，程序会跳过这些币种并记录日志
4. API调用频率限制：程序内置了请求限制处理，但如果遇到限流问题，可以尝试减小workers数量 