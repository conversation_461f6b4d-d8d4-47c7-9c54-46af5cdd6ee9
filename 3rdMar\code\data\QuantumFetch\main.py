import sys
sys.path.append("/data/code")
from core.http import *
from tornado.ioloop import IOLoop, PeriodicCallback
import data.QuantumFetch.source as S
import data.QuantumFetch.common as C
from data.QuantumFetch.common import gen_base_http_app




handlers = [
    (r'/api/v1/servertime', C.GetServerTimeHandler),
]


if __name__ == "__main__":
    app = gen_base_http_app(handlers)
    app.listen(5111)
    IOLoop.current().spawn_callback(S.bitget.demo)

    # PeriodicCallback(lambda: IOLoop.current().spawn_callback(C.hello), 1000 * 2).start()
    IOLoop.current().start()

