<think>
我需要根据提供的数据模型需求文档(datamodel_requirement.md)和现有的Python代码文件(coin_list_fetcher.py, coin_history_fetcher.py, coin_mkc_collector.py)来创建一份数据模块用户需求文档。

从提供的文件中，我了解到这是一个资产筛选机器人项目，当前任务是重构数据获取模块的框架。关键点包括：

1. 解耦coin_mkc_collector.py与coin_list_fetcher.py和coin_history_fetcher.py的硬编码关系
2. 将框架改造为对拓展开放，对修改关闭的设计
3. 数据获取模块包括数据源API、连接池和数据库三部分
4. 连接池需要抽象化、支持并发、可配置，并支持不同API的调用策略
5. 数据库需要支持列式存储和查询

我需要根据这些信息，编写一份完整的用户需求文档，内容应该包括项目背景、功能需求、性能需求、接口需求等方面。
</think>

# 数据模块用户需求文档

## 1. 项目背景

我们正在构建一个资产筛选机器人，该机器人需要能够从多个数据源获取加密货币相关的数据，对数据进行处理和存储，并最终支持筛选与图形化展示。

当前阶段的核心任务是对数据获取模块进行重构，将现有的硬编码关系解耦，打造一个灵活、可扩展的数据获取框架。

## 2. 功能需求

### 2.1 数据源 API 抽象层

- **需求描述**：创建数据源API的抽象层，使系统能够统一处理不同类型的数据源
- **具体要求**：
  - 支持不同类型数据源的统一接口定义
  - 支持将现有的币种列表API和币种历史数据API纳入抽象层
  - 支持未来可能添加的其他数据源类型
  - 每个数据源需定义必要的元数据（如API限制、请求头参数等）

### 2.2 连接池组件

- **需求描述**：设计高效的连接池组件，管理对各数据源的请求
- **具体要求**：
  - 提供统一的接口调用数据源API
  - 支持并发调用，最大化数据获取效率
  - 支持API调用前预处理、调用中处理、调用后后处理等钩子函数
  - 能够智能管理API请求速率限制，避免触发限流
  - 支持配置化调用参数和钩子函数
  - 支持不同API使用不同的调用策略（并行或串行）
  - 提供失败重试机制，可配置最大重试次数

### 2.3 数据存储组件

- **需求描述**：设计灵活的数据存储组件，支持数据的高效存取
- **具体要求**：
  - 支持将polars DataFrame数据保存为parquet等列式存储格式
  - 支持数据表的基本查询操作
  - 提供统一的数据存取接口，屏蔽底层存储实现差异
  - 支持未来可能的扩展，如接入MySQL、Redis等中间件
  - 支持基于时间戳、币种ID等维度的数据索引和检索

### 2.4 任务调度组件

- **需求描述**：实现可配置的任务调度机制，协调整个数据获取流程
- **具体要求**：
  - 支持批量任务的创建和调度
  - 提供任务优先级管理
  - 支持任务执行状态跟踪和异常处理
  - 支持数据获取流程的可视化监控

## 3. 性能需求

### 3.1 并发性能

- 支持至少30个并发工作线程
- 在API限制条件下，最大化数据获取效率
- 合理使用队列等机制，减少锁竞争

### 3.2 资源占用

- 内存使用合理，避免数据冗余和内存泄漏
- 处理大量数据时（如15000+币种）保持稳定性能

### 3.3 响应时间

- 数据获取任务的响应时间应在API限制条件下尽可能短
- 提供进度反馈机制，让用户了解任务执行状态

## 4. 接口需求

### 4.1 数据源接口

```python
class DataSourceAPI:
    """数据源API抽象接口"""
    
    def fetch_data(self, **params):
        """获取数据的通用方法"""
        pass
    
    def get_rate_limit(self):
        """获取API限制信息"""
        pass
```

### 4.2 连接池接口

```python
class ConnectionPool:
    """连接池抽象接口"""
    
    def execute(self, data_source, params, pre_hook=None, post_hook=None):
        """执行数据源调用"""
        pass
    
    def batch_execute(self, tasks, max_workers=10, strategy="parallel"):
        """批量执行数据源调用"""
        pass
```

### 4.3 数据存储接口

```python
class DataStorage:
    """数据存储抽象接口"""
    
    def save(self, dataframe, metadata):
        """保存数据"""
        pass
    
    def load(self, query):
        """加载数据"""
        pass
    
    def query(self, conditions):
        """查询数据"""
        pass
```

## 5. 扩展性需求

### 5.1 新数据源支持

- 框架应能够轻松添加新的数据源API，无需修改核心代码
- 支持不同数据源API的特定参数和处理逻辑

### 5.2 新存储方式支持

- 能够灵活切换或添加新的存储后端，如MySQL、Redis等
- 保持统一的数据访问接口

### 5.3 新数据处理策略

- 支持添加新的数据处理钩子
- 支持自定义调度策略和优先级规则

## 6. 兼容性需求

- 与现有的coin_list_fetcher.py和coin_history_fetcher.py保持功能兼容
- 支持现有的数据格式和处理流程
- 提供平滑迁移路径，避免对现有功能造成破坏

## 7. 文档需求

- 完整的API文档，包括每个组件的使用示例
- 架构设计文档，说明各组件之间的关系和交互方式
- 配置指南，详细说明各种配置选项和推荐设置

## 8. 测试需求

- 单元测试覆盖所有核心组件
- 集成测试验证组件间交互
- 性能测试评估并发性能和资源占用
- 模拟API限制的测试场景

## 9. 部署需求

- 支持作为独立模块使用
- 提供简单的配置示例和快速启动指南
- 支持通过pip安装所有依赖

## 10. 时间线

- 第一阶段：完成核心框架设计和接口定义（2周）
- 第二阶段：实现连接池和数据存储组件（3周）
- 第三阶段：与现有代码集成并进行测试（2周）
- 第四阶段：优化性能并完善文档（1周）
