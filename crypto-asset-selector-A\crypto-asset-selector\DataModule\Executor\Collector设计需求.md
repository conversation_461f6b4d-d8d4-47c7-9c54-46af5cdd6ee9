## 一、任务概要

现在我需要设计一个Collector来收集来自不同的数据源API的数据，请你参考coin_mkc_collector的结构，设计一个collector框架。

## 二、具体要求

### 1. Collector

请参考coin_mkc_collector.py的写法，将其抽象成一个框架，使得来自不同的数据源的API都可以使用这个collector的业务逻辑进行数据采集。

Collector基类使用模板模式，结构可参考以下代码：

```python
class BaseCollector:
    """API调用模板"""
    def run(self, params):
        self.pre_handle(params)
        result = self.handle(params)
        return self.post_handle(result)
        
    def pre_handle(self, params):
        # 默认实现或钩子
        pass
        
    def handle(self, params):
        # 具体实现由子类提供
        pass
        
    def post_handle(self, result):
        # 默认实现或钩子
        return result
```
其中，线程池管理、重试队列等逻辑可写在基类内，提高代码复用率。

同时使用装饰器模式，为每一个API创建一个具体的Collector实现类，每一个实现类独占一个文件，便于之后拓展，collector结构参考如下：

```python
class TokenInsightCoinHistoryCollector(BaseCollector):
    
    def run(self, params):
        pass
    def pre_handle(self, params):
        pass
    def handle(self, params):
        pass
    def post_handle(self, result):
        pass

```
```python

class TokenInsightCoinListCollector(BaseCollector):

    def run(self, params):
        pass
    def pre_handle(self, params):
        pass
    def handle(self, params):
        pass
    def post_handle(self, result):
        pass

```
在本次任务中，你需要参考FetcherApi中的两个fetcher的业务逻辑以及coin_mkc_collector中调用两个API的逻辑，将它们改写成collector实现类。其中，某API依赖于另一API的产出数据的业务逻辑，写在该API对应的Collector的prehandle方法中；某API产出标准数据的业务逻辑，写在该API对应的Collector的posthandle方法中。

### 2. 注册中心

注册中心在程序运行的一开始便通过读取配置，决定本次运行需要注册哪些Collector实现类：

```python
class CollectorRegistry:

    def register(self, config):
        for collector in config["Collectors"]:
            self.register_collector(collector)
    def register_collector(self, collector):
        pass

```
### 3. Config

配置结构可参考以下结构：

```yml
DataSource:
    {
        Name: TokenInsight,
        APIKey: xxxxxxxx,
        Collectors: [
            TokenInsightCoinHistoryCollector,  TokenInsightCoinListCollector,
            xxxxCollctor...
            ],
    }
    {
        Name: CoinGecko,
        APIKey: xxxxxxxx,
        Collectors: [
            CoinGeckoCoinHistoryCollector, 
            CoinGeckoCoinListCollector,
            xxxxCollector...
            ],
    }
    ...


```

### 4. 项目结构安排
请你在DataModule下安排相关的文件分类。
1. 可以有一个Collectors目录，用于存放BaseCollector与各个API的Collector实现类。
2. 可以有一个Cache目录，用于存放可能存在的中间产物数据，如coin_list_cache.parquet
3. Utils中存放可能用到的工具类。
4. DataModule下，直接有一个main.py作为DataModule的入口，同时有data_config.yaml规定DataModule的行为。
