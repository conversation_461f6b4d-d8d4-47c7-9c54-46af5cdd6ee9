import importlib
import os
from typing import Dict, Any, List, Type, Optional, Tuple

from .base_collector import BaseCollector

class CollectorRegistry:
    """收集器注册中心，管理所有注册的收集器"""
    
    _instance = None
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(CollectorRegistry, cls).__new__(cls)
            cls._instance._collectors = {}
            cls._instance._collector_api_info = {}  # 初始化保存的API信息
            cls._instance._collector_params = {}
            cls._instance._collector_dependencies = {}
            cls._instance._execution_flow = []
            cls._instance._collector_results = {}
        return cls._instance
    
    def __init__(self):
        """初始化注册中心"""
        self._collectors = getattr(self, '_collectors', {})
        self._collector_api_info = getattr(self, '_collector_api_info', {})  # 获取保存的API信息
        self._collector_params = getattr(self, '_collector_params', {})
        self._collector_dependencies = getattr(self, '_collector_dependencies', {})
        self._execution_flow = getattr(self, '_execution_flow', [])
        self._collector_results = getattr(self, '_collector_results', {})
    
    def register(self, config: Dict[str, Any]) -> None:
        """
        根据配置注册收集器
        
        参数:
        config (Dict): 配置信息，包含数据源和收集器列表
        """
        # 注册所有收集器
        for data_source in config.get("DataSource", []):
            source_name = data_source.get("Name")
            collectors = data_source.get("Collectors", [])
            api_key = data_source.get("APIKey")
            api_limit = data_source.get("APILimit")
            
            # 注册每个收集器
            for collector_info in collectors:
                if isinstance(collector_info, dict):
                    collector_name = collector_info.get("Name")
                    collector_params = collector_info.get("Params", {})
                    depends_on = collector_info.get("DependsOn")
                else:
                    collector_name = collector_info
                    collector_params = {}
                    depends_on = None
                
                # 注册收集器
                self.register_collector(collector_name, source_name, api_key, api_limit)
                
                # 保存收集器参数
                collector_key = f"{source_name}.{collector_name}" if source_name else collector_name
                self._collector_params[collector_key] = collector_params
                
                # 保存依赖关系
                if depends_on:
                    depends_key = f"{source_name}.{depends_on}" if source_name else depends_on
                    self._collector_dependencies[collector_key] = depends_key
        
        # 设置执行流程
        execution = config.get("Execution", {})
        self._execution_flow = execution.get("Flow", [])
    
    def register_collector(self, collector_name: str, source_name: Optional[str] = None, api_key: str = None, api_limit: int = None) -> None:
        """
        注册单个收集器
        
        参数:
        collector_name (str): 收集器类名
        source_name (str, optional): 数据源名称
        """
        try:
            # 根据导入方式尝试不同的模块路径
            module_paths = [
                f"DataModule.Collectors.{collector_name.lower()}",  # 绝对导入路径
                f".{collector_name.lower()}"                      # 相对导入路径
            ]
            
            module = None
            for path in module_paths:
                try:
                    if path.startswith('.'):
                        # 如果是相对导入，使用importlib.import_module从当前包导入
                        module = importlib.import_module(path, package="DataModule.Collectors")
                    else:
                        # 否则使用绝对导入
                        module = importlib.import_module(path)
                    if module:
                        break
                except ImportError:
                    continue
            
            if not module:
                raise ImportError(f"无法导入模块: {collector_name.lower()}")
            
            collector_class = getattr(module, collector_name)
            
            # 确保该类是BaseCollector的子类
            if not issubclass(collector_class, BaseCollector):
                raise TypeError(f"{collector_name} 不是 BaseCollector 的子类")
            
            # 注册收集器
            key = f"{source_name}.{collector_name}" if source_name else collector_name
            self._collectors[key] = collector_class
            self._collector_api_info[key] = {"api_key": api_key, "api_limit": api_limit}
            print(f"已注册收集器: {key}")
            
        except (ImportError, AttributeError) as e:
            print(f"注册收集器 {collector_name} 失败: {e}")
    
    def get_collector(self, collector_name: str, source_name: Optional[str] = None, **kwargs) -> Optional[BaseCollector]:
        """
        获取收集器实例
        
        参数:
        collector_name (str): 收集器类名
        source_name (str, optional): 数据源名称
        **kwargs: 传递给收集器构造函数的参数
        
        返回:
        BaseCollector: 收集器实例，如果未找到则返回None
        """
        key = f"{source_name}.{collector_name}" if source_name else collector_name
        collector_class = self._collectors.get(key)
        api_info = self._collector_api_info.get(key)
        api_key = api_info.get("api_key")
        api_limit = api_info.get("api_limit")
        
        if collector_class:
            kwargs = {**kwargs, "api_key": api_key, "api_limit": api_limit}
            return collector_class(**kwargs)
        
        print(f"未找到收集器: {key}")
        return None
    
    def get_collector_class(self, collector_name: str, source_name: Optional[str] = None) -> Optional[Type[BaseCollector]]:
        """
        获取收集器类
        
        参数:
        collector_name (str): 收集器类名
        source_name (str, optional): 数据源名称
        
        返回:
        Type[BaseCollector]: 收集器类，如果未找到则返回None
        """
        key = f"{source_name}.{collector_name}" if source_name else collector_name
        return self._collectors.get(key)
    
    def list_collectors(self) -> List[str]:
        """
        列出所有注册的收集器
        
        返回:
        List[str]: 收集器名称列表
        """
        return list(self._collectors.keys())
    
    def execute_pipeline(self, global_settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行收集器流水线
        
        参数:
        global_settings (Dict): 全局设置参数
        
        返回:
        Dict: 所有收集器的执行结果
        """
        # 清空上一次的执行结果
        self._collector_results = {}
        
        # 如果没有设置执行流程，则默认执行所有注册的收集器
        if not self._execution_flow:
            self._execution_flow = list(self._collectors.keys())
        
        # 执行每个收集器
        for collector_key in self._execution_flow:
            result = self.execute_collector(collector_key, global_settings)
            self._collector_results[collector_key] = result
        
        return self._collector_results
    
    def execute_collector(self, collector_key: str, global_settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行单个收集器
        
        参数:
        collector_key (str): 收集器键名（数据源名.收集器名）
        global_settings (Dict): 全局设置参数
        
        返回:
        Dict: 收集器执行结果
        """
        # 检查是否有依赖
        dependency_key = self._collector_dependencies.get(collector_key)
        dependency_result = None
        
        if dependency_key:
            # 如果依赖项尚未执行，先执行依赖项
            if dependency_key not in self._collector_results:
                dependency_result = self.execute_collector(dependency_key, global_settings)
                self._collector_results[dependency_key] = dependency_result
            else:
                dependency_result = self._collector_results[dependency_key]
        
        # 解析收集器键名
        if "." in collector_key:
            source_name, collector_name = collector_key.split(".", 1)
        else:
            source_name, collector_name = None, collector_key
        
        # 获取收集器特定参数
        collector_params = self._collector_params.get(collector_key, {})
        
        # 合并全局设置
        params = {**global_settings}
        
        # 创建收集器实例
        collector = self.get_collector(
            collector_name, 
            source_name,
            workers=global_settings.get("Workers", 12),
            max_retries=global_settings.get("MaxRetries", 3),
        )
        
        if not collector:
            return {"success": False, "message": f"无法创建收集器实例: {collector_key}"}
        
        # 根据依赖结果处理参数
        if dependency_result and dependency_result.get("success", False):
            # 为所有收集器提供一个通用的依赖数据传递方式
            params["dependency_data"] = dependency_result.get("data")
            
            # 特殊处理币种历史数据收集器依赖币种列表收集器的情况
            if collector_name == "TokenInsightCoinHistoryCollector" and dependency_key.endswith("TokenInsightCoinListCollector"):
                coins_df = dependency_result.get("data")
                if coins_df is not None:
                    params["coins"] = coins_df.select(["id", "symbol"]).to_dicts()
        
        # 合并收集器特定参数
        params.update(collector_params)
        
        # 执行收集器
        print(f"执行收集器: {collector_key}")
        result = collector.run(params)
        
        return result


# 单例实例
registry = CollectorRegistry() 