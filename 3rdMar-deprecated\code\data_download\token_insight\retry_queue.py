import asyncio
import logging
from typing import Callable, Any, Dict, Optional, Awaitable
from collections import deque

logger = logging.getLogger(__name__)


# TODO: 加入返回值队列
class RetryQueue:
    """
    通用异步重试队列
    支持任何类型的异步任务重试，具有灵活的任务处理和回调机制
    """
    
    def __init__(self, 
                 max_retries: int = 3,
                 retry_delay: float = 0.5,
                 max_concurrent_retries: int = 5,
                 enable_exponential_backoff: bool = True):
        """
        初始化重试队列
        
        参数:
        max_retries (int): 最大重试次数，默认3
        retry_delay (float): 重试间隔时间（秒），默认0.5
        max_concurrent_retries (int): 最大并发重试数量，默认5
        enable_exponential_backoff (bool): 是否启用指数退避，默认True
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.max_concurrent_retries = max_concurrent_retries
        self.enable_exponential_backoff = enable_exponential_backoff
        
        # 重试队列
        self.retry_queue = asyncio.Queue()
        
        # 用于控制并发重试的信号量
        self.retry_semaphore = asyncio.Semaphore(max_concurrent_retries)
        
        # 标记是否正在处理重试队列
        self.processing_retries = False
        
        # 统计信息
        self.total_retries = 0
        self.successful_retries = 0
        self.failed_retries = 0
    
    async def add_task(self, 
                      task_func: Callable[..., Awaitable[Any]], 
                      task_args: tuple = (), 
                      task_kwargs: Dict[str, Any] = None,
                      task_id: str = None,
                      retry_count: int = 0,
                      metadata: Dict[str, Any] = None):
        """
        添加任务到重试队列
        
        参数:
        task_func (Callable): 要重试的异步函数
        task_args (tuple): 函数的位置参数
        task_kwargs (Dict): 函数的关键字参数
        task_id (str): 任务ID，用于日志记录
        retry_count (int): 当前重试次数
        metadata (Dict): 任务元数据，用于传递额外信息
        """
        if task_kwargs is None:
            task_kwargs = {}
        if metadata is None:
            metadata = {}
        
        task_item = {
            "task_func": task_func,
            "task_args": task_args,
            "task_kwargs": task_kwargs,
            "task_id": task_id or f"task_{id(task_func)}",
            "retry_count": retry_count,
            "metadata": metadata
        }
        
        await self.retry_queue.put(task_item)
        logger.debug(f"任务 {task_item['task_id']} 已添加到重试队列，重试次数: {retry_count}")
    
    async def process_single_retry(self, task_item: Dict[str, Any]) -> bool:
        """
        处理单个重试任务
        
        参数:
        task_item (Dict): 任务项
        
        返回:
        bool: 任务是否成功
        """
        async with self.retry_semaphore:
            task_func = task_item["task_func"]
            task_args = task_item["task_args"]
            task_kwargs = task_item["task_kwargs"]
            task_id = task_item["task_id"]
            retry_count = task_item["retry_count"]
            metadata = task_item["metadata"]
            
            # 计算等待时间
            if self.enable_exponential_backoff and retry_count > 0:
                wait_time = self.retry_delay * (2 ** (retry_count - 1))
            else:
                wait_time = self.retry_delay
            
            if retry_count > 0:
                logger.info(f"重试任务 {task_id}，第 {retry_count} 次重试，等待 {wait_time:.2f} 秒")
                await asyncio.sleep(wait_time)
            
            try:
                # 执行任务
                result = await task_func(*task_args, **task_kwargs)
                
                # 检查任务是否成功（假设返回值为布尔值或包含成功标志的元组）
                if isinstance(result, tuple):
                    success = result[0] if len(result) > 0 else False
                else:
                    success = bool(result)
                
                if success:
                    self.successful_retries += 1
                    logger.info(f"重试任务 {task_id} 成功")
                    return True
                else:
                    # 如果还有重试次数，重新加入队列
                    if retry_count < self.max_retries:
                        await self.add_task(
                            task_func=task_func,
                            task_args=task_args,
                            task_kwargs=task_kwargs,
                            task_id=task_id,
                            retry_count=retry_count + 1,
                            metadata=metadata
                        )
                        logger.warning(f"任务 {task_id} 失败，将进行第 {retry_count + 1} 次重试")
                    else:
                        self.failed_retries += 1
                        logger.error(f"任务 {task_id} 重试失败，已达到最大重试次数 {self.max_retries}")
                    
                    return False
                    
            except Exception as e:
                logger.error(f"重试任务 {task_id} 时出错: {e}")
                
                # 如果还有重试次数，重新加入队列
                if retry_count < self.max_retries:
                    await self.add_task(
                        task_func=task_func,
                        task_args=task_args,
                        task_kwargs=task_kwargs,
                        task_id=task_id,
                        retry_count=retry_count + 1,
                        metadata=metadata
                    )
                    logger.warning(f"任务 {task_id} 异常，将进行第 {retry_count + 1} 次重试")
                else:
                    self.failed_retries += 1
                    logger.error(f"任务 {task_id} 重试失败，已达到最大重试次数 {self.max_retries}")
                
                return False
    
    async def process_retry_queue(self):
        """
        处理重试队列中的所有任务
        """
        if self.processing_retries:
            logger.debug("重试队列已在处理中，跳过")
            return
        
        self.processing_retries = True
        logger.info("开始处理重试队列")
        
        try:
            while not self.retry_queue.empty():
                task_item = await self.retry_queue.get()
                self.total_retries += 1
                
                # 处理重试任务
                await self.process_single_retry(task_item)
                
                # 标记任务完成
                self.retry_queue.task_done()
                
        except Exception as e:
            logger.error(f"处理重试队列时出错: {e}")
        finally:
            self.processing_retries = False
            logger.info(f"重试队列处理完毕，统计: 总重试 {self.total_retries}, 成功 {self.successful_retries}, 失败 {self.failed_retries}")
    
    async def start_background_processor(self):
        """
        启动后台重试队列处理器
        
        返回:
        asyncio.Task: 后台任务
        """
        async def background_processor():
            while True:
                try:
                    if not self.retry_queue.empty() and not self.processing_retries:
                        await self.process_retry_queue()
                    await asyncio.sleep(1)  # 每秒检查一次
                except asyncio.CancelledError:
                    logger.info("后台重试队列处理器已停止")
                    break
                except Exception as e:
                    logger.error(f"后台重试队列处理器出错: {e}")
                    await asyncio.sleep(5)  # 出错后等待5秒再继续
        
        return asyncio.create_task(background_processor())
    
    async def wait_for_completion(self):
        """
        等待所有重试任务完成
        """
        await self.retry_queue.join()
        
        # 确保最后一轮重试也完成
        if not self.retry_queue.empty():
            await self.process_retry_queue()
    
    def get_stats(self) -> Dict[str, int]:
        """
        获取重试队列统计信息
        
        返回:
        Dict[str, int]: 统计信息
        """
        return {
            "total_retries": self.total_retries,
            "successful_retries": self.successful_retries,
            "failed_retries": self.failed_retries,
            "pending_retries": self.retry_queue.qsize()
        }
    
    def reset_stats(self):
        """
        重置统计信息
        """
        self.total_retries = 0
        self.successful_retries = 0
        self.failed_retries = 0
