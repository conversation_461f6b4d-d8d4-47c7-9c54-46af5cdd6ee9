
# 工作流与依赖管理演示代码

以下是一个完整演示，结合设计需求中的模板模式和策略模式，实现了工作流与依赖管理的配置驱动解决方案：

```python
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Any

# 模板模式：基本收集器
class BaseCollector:
    """API调用模板"""
    def __init__(self, config):
        self.config = config
        
    def run(self, params):
        self.pre_handle(params)
        result = self.handle(params)
        return self.post_handle(result)
        
    def pre_handle(self, params):
        # 默认实现或钩子
        pass
        
    def handle(self, params):
        # 具体实现由子类提供
        raise NotImplementedError("子类必须实现handle方法")
        
    def post_handle(self, result):
        # 默认实现或钩子
        return result

# 策略模式：并行收集器
class ParallelCollector(BaseCollector):
    def __init__(self, config):
        super().__init__(config)
        self.workers = config.get("workers", 5)
    
    def handle(self, params):
        if "param_iterator" in self.config:
            iterator_config = self.config["param_iterator"]
            source_list = params.get(iterator_config["iterate_on"], [])
            results = []
            
            with ThreadPoolExecutor(max_workers=self.workers) as executor:
                futures = []
                
                for item in source_list:
                    iter_params = params.copy()
                    iter_params[iterator_config["iterate_as"]] = item
                    
                    # 移除迭代源列表，避免不必要的传递
                    if iterator_config["iterate_on"] in iter_params:
                        del iter_params[iterator_config["iterate_on"]]
                    
                    futures.append(executor.submit(self._process_item, iter_params))
                
                for future in futures:
                    result = future.result()
                    if result:
                        results.append(result)
            
            return self._merge_results(results)
        else:
            # 调用API的具体实现
            return self._call_api(params)
    
    def _process_item(self, params):
        # 模拟API调用
        print(f"并行处理项目: {params}")
        return self._call_api(params)
    
    def _call_api(self, params):
        # 根据config配置调用实际API
        api_config = self.config.get("api_config", {})
        url_template = api_config.get("url", "")
        method = api_config.get("method", "GET")
        
        # 格式化URL中的参数
        url = url_template
        for key, value in params.items():
            placeholder = f"{{{key}}}"
            if placeholder in url:
                url = url.replace(placeholder, str(value))
        
        print(f"调用API: {method} {url}")
        
        # 模拟API调用 - 在实际应用中，这里会调用真实的API
        if "coin_list" in self.config.get("task_id", ""):
            # 模拟币种列表API
            return {
                "data": {
                    "id_list": ["bitcoin", "ethereum", "ripple"],
                    "symbols": ["BTC", "ETH", "XRP"]
                }
            }
        elif "coin_history" in self.config.get("task_id", ""):
            # 模拟币种历史API
            coin_id = params.get("coin_id", "unknown")
            return {
                "coin_id": coin_id,
                "history": [{"date": "2023-01-01", "price": 100}, {"date": "2023-01-02", "price": 105}]
            }
        
        return {"status": "success", "params": params}
    
    def _merge_results(self, results):
        # 合并多个结果
        if not results:
            return {"data": []}
        
        return {
            "data": results
        }

# 策略模式：串行收集器
class SerialCollector(BaseCollector):
    def handle(self, params):
        if "param_iterator" in self.config:
            iterator_config = self.config["param_iterator"]
            source_list = params.get(iterator_config["iterate_on"], [])
            results = []
            
            for item in source_list:
                iter_params = params.copy()
                iter_params[iterator_config["iterate_as"]] = item
                
                # 移除迭代源列表，避免不必要的传递
                if iterator_config["iterate_on"] in iter_params:
                    del iter_params[iterator_config["iterate_on"]]
                
                result = self._call_api(iter_params)
                if result:
                    results.append(result)
            
            return self._merge_results(results)
        else:
            # 调用API的具体实现
            return self._call_api(params)
    
    def _call_api(self, params):
        # 与ParallelCollector中相同，实际应用可以提取到共享的辅助类中
        api_config = self.config.get("api_config", {})
        url_template = api_config.get("url", "")
        method = api_config.get("method", "GET")
        
        # 格式化URL中的参数
        url = url_template
        for key, value in params.items():
            placeholder = f"{{{key}}}"
            if placeholder in url:
                url = url.replace(placeholder, str(value))
        
        print(f"顺序调用API: {method} {url}")
        
        # 模拟API调用
        time.sleep(0.5)  # 模拟API延迟
        
        if "coin_list" in self.config.get("task_id", ""):
            # 模拟币种列表API
            return {
                "data": {
                    "id_list": ["bitcoin", "ethereum", "ripple"],
                    "symbols": ["BTC", "ETH", "XRP"]
                }
            }
        elif "coin_history" in self.config.get("task_id", ""):
            # 模拟币种历史API
            coin_id = params.get("coin_id", "unknown")
            return {
                "coin_id": coin_id,
                "history": [{"date": "2023-01-01", "price": 100}, {"date": "2023-01-02", "price": 105}]
            }
        
        return {"status": "success", "params": params}
    
    def _merge_results(self, results):
        # 合并多个结果
        if not results:
            return {"data": []}
        
        return {
            "data": results
        }

# 工厂模式：策略工厂
class StrategyFactory:
    """连接工厂"""
    @staticmethod
    def create_collector(config):
        task_id = config.get("task_id", "unknown")
        if config.get("strategy") == "parallel":
            print(f"为任务 '{task_id}' 创建并行收集器")
            return ParallelCollector(config)
        elif config.get("strategy") == "serial":
            print(f"为任务 '{task_id}' 创建串行收集器")
            return SerialCollector(config)
        else:
            raise ValueError(f"不支持的策略类型: {config.get('strategy')}")

# 工作流执行器
class WorkflowExecutor:
    def __init__(self, config):
        self.workflow_config = config
        self.results_cache = {}  # 存储中间结果
        
    def execute(self):
        """执行工作流中的所有任务"""
        execution_order = self.workflow_config.get("execution_order", [])
        
        if not execution_order:
            # 如果没有指定执行顺序，获取所有任务ID
            execution_order = list(self.workflow_config.get("tasks", {}).keys())
        
        print(f"工作流执行顺序: {execution_order}")
        
        for task_id in execution_order:
            if task_id not in self.results_cache:
                self._execute_task(task_id)
        
        return self.results_cache
    
    def _execute_task(self, task_id):
        """执行单个任务"""
        if task_id not in self.workflow_config.get("tasks", {}):
            raise ValueError(f"任务 '{task_id}' 未在配置中定义")
        
        task_config = self.workflow_config["tasks"][task_id]
        task_config["task_id"] = task_id  # 添加任务ID到配置中
        
        print(f"\n执行任务: {task_id}")
        
        # 处理依赖
        dependencies = task_config.get("dependencies", [])
        for dep_id in dependencies:
            # 检查依赖任务的缓存配置
            dep_config = self.workflow_config["tasks"].get(dep_id, {})
            cache_config = dep_config.get("cache_config", {})
            cache_enabled = cache_config.get("enabled", False)
            cache_path = cache_config.get("path", f"cache/{dep_id}.json")
            cache_ttl = cache_config.get("ttl", 3600)  # 默认缓存有效期1小时
            
            # 检查是否有有效缓存
            if cache_enabled and self._check_valid_cache(cache_path, cache_ttl):
                print(f"任务 '{task_id}' 依赖于 '{dep_id}'，使用本地缓存")
                self.results_cache[dep_id] = self._load_cache(cache_path)
            elif dep_id not in self.results_cache:
                print(f"任务 '{task_id}' 依赖于 '{dep_id}'，先执行依赖任务")
                self._execute_task(dep_id)
                
                # 如果启用了缓存，保存结果到缓存
                if cache_enabled:
                    self._save_cache(self.results_cache[dep_id], cache_path)
        
        # 准备参数，包括依赖项的结果
        params = task_config.get("params", {}).copy()
        
        # 合并依赖项的输出到当前任务的输入
        for dep_id in dependencies:
            dependency_mapping = task_config.get("dependency_mapping", {}).get(dep_id, {})
            
            if dependency_mapping:
                print(f"应用依赖映射: {dep_id} -> {task_id}")
                for src_path, dest_key in dependency_mapping.items():
                    # 支持点表示法访问嵌套字典
                    value = self._get_nested_dict_value(self.results_cache[dep_id], src_path)
                    
                    if value is not None:
                        print(f"映射 '{src_path}' -> '{dest_key}': {value}")
                        params[dest_key] = value
                    else:
                        print(f"警告: 在依赖结果中未找到路径 '{src_path}'")
        
        # 创建并执行收集器
        collector = StrategyFactory.create_collector(task_config)
        result = collector.run(params)
        
        # 缓存结果
        self.results_cache[task_id] = result
        print(f"任务 '{task_id}' 执行完成，结果: {result}")
    
    def _get_nested_dict_value(self, data, path):
        """使用点表示法从嵌套字典中获取值"""
        keys = path.split(".")
        value = data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        
        return value

    def _check_valid_cache(self, cache_path, ttl):
        """检查缓存是否存在且有效"""
        import os
        import time
        
        if not os.path.exists(cache_path):
            return False
            
        # 检查缓存文件的修改时间
        file_mtime = os.path.getmtime(cache_path)
        current_time = time.time()
        
        # 如果缓存文件的修改时间在TTL范围内，则认为缓存有效
        return (current_time - file_mtime) < ttl
    
    def _load_cache(self, cache_path):
        """从缓存文件加载数据"""
        import json
        
        try:
            with open(cache_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载缓存失败: {e}")
            return None
    
    def _save_cache(self, data, cache_path):
        """保存数据到缓存文件"""
        import json
        import os
        
        # 确保缓存目录存在
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        
        try:
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已缓存到: {cache_path}")
        except Exception as e:
            print(f"保存缓存失败: {e}")

# 示例配置
workflow_config = {
    "tasks": {
        "coin_list": {
            "strategy": "parallel",
            "api_config": {
                "url": "https://api.example.com/coins",
                "method": "GET"
            },
            "params": {
                "limit": 100,
                "offset": 0
            },
            "cache_config": {
                "enabled": True,
                "path": "cache/coin_list.json",
                "ttl": 86400  # 24小时缓存有效期
            }
        },
        "coin_history": {
            "strategy": "serial",
            "api_config": {
                "url": "https://api.example.com/coins/{coin_id}/history",
                "method": "GET"
            },
            "dependencies": ["coin_list"],
            "dependency_mapping": {
                "coin_list": {
                    "data.id_list": "coin_ids"
                }
            },
            "param_iterator": {
                "iterate_on": "coin_ids",
                "iterate_as": "coin_id"
            }
        }
    },
    "execution_order": ["coin_list", "coin_history"]
}

# 运行演示
if __name__ == "__main__":
    print("启动配置驱动的数据收集引擎演示")
    print("==================================")
    
    # 创建并执行工作流
    executor = WorkflowExecutor(workflow_config)
    results = executor.execute()
    
    print("\n工作流执行结果摘要:")
    for task_id, result in results.items():
        if isinstance(result, dict) and "data" in result:
            if isinstance(result["data"], list):
                print(f"任务 '{task_id}' 收集了 {len(result['data'])} 条数据")
            else:
                print(f"任务 '{task_id}' 收集了数据，类型: {type(result['data'])}")
        else:
            print(f"任务 '{task_id}' 完成")
```

## 设计要点说明

1. **模板模式**：`BaseCollector` 类提供了数据收集的基本框架，定义了 `pre_handle`、`handle` 和 `post_handle` 三个步骤。

2. **策略模式**：`ParallelCollector` 和 `SerialCollector` 是两种不同的收集策略，分别支持并行和串行处理。

3. **工厂模式**：`StrategyFactory` 根据配置创建适当的收集器实例。

4. **依赖管理**：`WorkflowExecutor` 在执行任务前会先检查其依赖，确保依赖任务先执行完成，并将依赖任务的结果传递给当前任务。

5. **参数迭代**：配置中的 `param_iterator` 支持对列表参数进行迭代，适用于需要批量处理的场景。

这个演示展示了如何使用配置文件来驱动整个数据收集流程，特别是处理API之间的依赖关系。通过这种方式，您可以仅通过修改配置文件来支持新的数据源，而无需修改代码。
