import os
import re
import csv
import sys
import time
import random
import requests
from bs4 import BeautifulSoup
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import argparse

# 预先导入所有可能使用的浏览器模块
try:
    # Edge浏览器相关模块
    from selenium.webdriver.edge.service import Service as EdgeService
    from selenium.webdriver.edge.options import Options as EdgeOptions
    from webdriver_manager.microsoft import EdgeChromiumDriverManager
    
    # Chrome浏览器相关模块
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from webdriver_manager.chrome import ChromeDriverManager
except ImportError as e:
    print(f"警告: 导入浏览器模块时出错: {e}")
    print("您可能需要安装相关依赖: pip install webdriver-manager selenium")

def scrape_from_url(url, scroll_times=20, scroll_pause_time=2, max_no_new_comments=10, headless=True, browser_type="edge"):
    """
    直接从URL抓取X-GPT网页内容，处理动态加载
    :param url: 要抓取的URL
    :param scroll_times: 滚动次数，用于加载更多内容
    :param scroll_pause_time: 每次滚动后的等待时间
    :param max_no_new_comments: 允许连续多少次滚动没有新评论后结束
    :param headless: 是否使用无头模式
    :param browser_type: 浏览器类型，可选值为"edge"或"chrome"
    :return: 包含评论数据的列表
    """
    try:
        print(f"正在初始化{browser_type.capitalize()}浏览器...")
        
        # 选择浏览器类型
        if browser_type.lower() == "chrome":
            # 配置Chrome选项
            browser_options = ChromeOptions()
            if headless:
                browser_options.add_argument("--headless=new")  # Chrome新版本的无头模式
            browser_options.add_argument("--disable-gpu")
            browser_options.add_argument("--window-size=1920,1080")
            browser_options.add_argument("--disable-extensions")
            
            # 设置用户代理
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Chrome/96.0.4664.55 Safari/605.1.15",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36"
            ]
            browser_options.add_argument(f"--user-agent={random.choice(user_agents)}")
            
            # 初始化Chrome浏览器
            driver = webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()), options=browser_options)
            
        else:  # 默认使用Edge
            # 配置Edge选项
            browser_options = EdgeOptions()
            if headless:
                browser_options.add_argument("--headless")
            browser_options.add_argument("--disable-gpu")
            browser_options.add_argument("--window-size=1920,1080")
            browser_options.add_argument("--disable-extensions")
            
            # 设置用户代理
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36 Edg/97.0.1072.62",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) EdgiOS/46.3.7 Version/15.0 Safari/605.1.15",
                "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Mobile Safari/537.36 EdgA/96.0.1054.36"
            ]
            browser_options.add_argument(f"--user-agent={random.choice(user_agents)}")
            
            # 初始化Edge浏览器
            driver = webdriver.Edge(service=EdgeService(EdgeChromiumDriverManager().install()), options=browser_options)
        
        # 设置页面加载超时
        driver.set_page_load_timeout(30)
        
        print(f"正在访问 {url}...")
        driver.get(url)
        
        # 等待页面加载 - 确保vue-recycle-scroller已加载
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CLASS_NAME, "tweet-container"))
        )
        
        # 尝试定位vue-recycle-scroller元素
        try:
            scroller_element = driver.find_element(By.CLASS_NAME, "vue-recycle-scroller") or \
                              driver.find_element(By.CLASS_NAME, "recycle-scroller") or \
                              driver.find_element(By.CSS_SELECTOR, "[data-v-recycle-scroller]")
            print("已找到vue-recycle-scroller元素！")
        except:
            print("未找到vue-recycle-scroller元素，将使用常规滚动策略")
            scroller_element = None
        
        # 获取页面尺寸信息
        viewport_height = driver.execute_script("return window.innerHeight")
        page_height = driver.execute_script("return document.body.scrollHeight")
        print(f"视口高度: {viewport_height}px, 页面总高度: {page_height}px")
        
        # 先尝试分析页面，查找可能的滚动容器
        possible_scroll_containers = []
        try:
            # 查找所有可能的滚动容器
            containers = driver.find_elements(By.CSS_SELECTOR, "div[style*='overflow'], div[style*='scroll'], div.scroll, div.scrollable, main, section")
            for container in containers:
                try:
                    overflow = container.value_of_css_property("overflow")
                    overflow_y = container.value_of_css_property("overflow-y")
                    height = container.value_of_css_property("height")
                    
                    if (overflow in ['scroll', 'auto'] or overflow_y in ['scroll', 'auto']) and height not in ['0px', 'auto']:
                        possible_scroll_containers.append(container)
                        print(f"找到可能的滚动容器: {container.get_attribute('class') or container.tag_name}")
                except:
                    pass
        except:
            print("分析滚动容器时出错")
        
        print(f"页面已加载，开始执行滚动加载更多内容...")
        
        # 直接执行JavaScript来访问Vue组件的内部数据
        # 注意：此方法依赖于Vue开发者工具的访问，可能不总是有效
        try:
            vue_data_script = """
            if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
                let instances = window.__VUE_DEVTOOLS_GLOBAL_HOOK__.Vue.prototype.$instances || [];
                for (let instance of instances) {
                    if (instance.$options && instance.$options.name === 'RecycleScroller') {
                        return {
                            itemsCount: instance.itemsCount,
                            buffer: instance.buffer,
                            keyField: instance.keyField
                        };
                    }
                }
            }
            return null;
            """
            vue_data = driver.execute_script(vue_data_script)
            if vue_data:
                print(f"Vue RecycleScroller信息: 项目数量={vue_data['itemsCount']}, 缓冲区={vue_data['buffer']}, 键字段={vue_data['keyField']}")
        except:
            print("无法访问Vue组件内部数据")
        
        # 使用字典来存储唯一评论，键为内容的唯一标识
        unique_comments = {}
        
        # 记录没有新增评论的次数
        no_new_comments_count = 0
        
        # 记录见过的交易员名称 - 用于跟踪我们已经处理过的数据
        seen_trader_names = set()
        
        # 执行滚动以加载更多内容
        for i in range(scroll_times):
            actual_pause = scroll_pause_time + random.uniform(-0.3, 0.7)
            if actual_pause < 0.5:
                actual_pause = 0.5
                
            print(f"正在执行第 {i+1}/{scroll_times} 次滚动... (等待时间: {actual_pause:.2f}秒)")
            
            # 使用专门针对vue-recycle-scroller的滚动策略
            # 策略1: 尝试直接滚动vue-recycle-scroller元素
            if scroller_element:
                try:
                    # 使用JavaScript直接滚动vue-recycle-scroller元素
                    driver.execute_script("arguments[0].scrollTop += arguments[1];", 
                                         scroller_element, 
                                         random.randint(300, 800))
                except:
                    print("滚动vue-recycle-scroller元素失败，回退到常规滚动")
                    driver.execute_script("window.scrollBy(0, arguments[0]);", random.randint(300, 800))
            
            # 策略2: 尝试滚动可能的内部容器
            elif possible_scroll_containers:
                for container in random.sample(possible_scroll_containers, min(3, len(possible_scroll_containers))):
                    try:
                        driver.execute_script("arguments[0].scrollTop += arguments[1];", 
                                            container, 
                                            random.randint(300, 800))
                        time.sleep(random.uniform(0.2, 0.5))
                    except:
                        pass
            
            # 策略3: 常规滚动页面
            else:
                # 进行随机滚动，尝试触发vue-recycle-scroller的数据加载
                # 随机滚动距离
                scroll_distance = random.randint(300, 800)
                driver.execute_script(f"window.scrollBy(0, {scroll_distance});")
            
            # 等待内容加载
            time.sleep(actual_pause)
            
            # 每3次滚动尝试一次随机策略
            if i % 3 == 2:
                random_strategy = random.randint(1, 5)
                if random_strategy == 1:
                    # 往回滚动一点再往下
                    print("策略: 往回滚动再向下")
                    driver.execute_script("window.scrollBy(0, -200);")
                    time.sleep(0.5)
                    driver.execute_script("window.scrollBy(0, 300);")
                elif random_strategy == 2:
                    # 滚动到顶部然后快速滚动到当前位置
                    print("策略: 滚动到顶部再回到当前位置")
                    current_pos = driver.execute_script("return window.pageYOffset;")
                    driver.execute_script("window.scrollTo(0, 0);")
                    time.sleep(0.5)
                    driver.execute_script(f"window.scrollTo(0, {current_pos});")
                elif random_strategy == 3:
                    # 尝试点击某个元素触发事件
                    print("策略: 点击元素触发事件")
                    try:
                        containers = driver.find_elements(By.CLASS_NAME, "tweet-container")
                        if containers:
                            container = random.choice(containers)
                            # 避免点击链接
                            driver.execute_script("arguments[0].click();", container)
                    except:
                        pass
                elif random_strategy == 4:
                    # 尝试执行快速连续滚动
                    print("策略: 快速连续滚动")
                    for _ in range(3):
                        driver.execute_script(f"window.scrollBy(0, {random.randint(100, 300)});")
                        time.sleep(0.2)
                elif random_strategy == 5:
                    # 尝试直接触发滚动事件
                    print("策略: 触发滚动事件")
                    driver.execute_script("""
                    var event = new Event('scroll');
                    document.dispatchEvent(event);
                    """)
            
            # 重要：直接定位我们关心的数据元素
            # 1. 交易员名称: class="name cursor-pointer"
            # 2. 情感态度: class="tweet-gpt-sentiment-xx" (xx可能是negative/neutral/positive)
            # 3. 资产: 包含"Asset Involved:"的文本
            
            # 抓取当前所有可见的tweet-container
            containers = driver.find_elements(By.CLASS_NAME, "tweet-container")
            print(f"当前可见容器数量: {len(containers)}")
            
            # 收集当前的交易员名称，检查是否有新的
            current_traders = set()
            for container in containers:
                try:
                    # 提取交易员名称
                    name_element = container.find_element(By.CSS_SELECTOR, ".name.cursor-pointer")
                    if name_element:
                        trader_name = name_element.text.strip()
                        current_traders.add(trader_name)
                except:
                    pass
            
            new_traders = current_traders - seen_trader_names
            print(f"发现 {len(new_traders)} 个新交易员: {', '.join(list(new_traders)[:5])}")
            seen_trader_names.update(current_traders)
            
            # 直接从页面提取我们关心的数据
            current_items = []
            for container in containers:
                try:
                    # 1. 提取交易员名称
                    trader_name = None
                    try:
                        name_element = container.find_element(By.CSS_SELECTOR, ".name.cursor-pointer")
                        trader_name = name_element.text.strip()
                    except:
                        continue  # 如果没有交易员名称，跳过此容器
                    
                    # 2. 提取情感态度
                    sentiment = "neutral"  # 默认为中性
                    try:
                        # 查找所有可能的sentiment类
                        for sentiment_type in ["positive", "negative", "neutral"]:
                            sentiment_class = f"tweet-gpt-sentiment-{sentiment_type}"
                            sentiment_elements = container.find_elements(By.CLASS_NAME, sentiment_class)
                            if sentiment_elements:
                                sentiment = sentiment_type
                                break
                    except:
                        pass  # 如果无法确定情感，使用默认值
                    
                    # 3. 提取资产
                    asset = ""
                    try:
                        # 寻找包含"Asset Involved:"的元素
                        container_text = container.text
                        if "Asset Involved:" in container_text:
                            # 尝试提取资产信息
                            text_parts = container_text.split("Asset Involved:")
                            if len(text_parts) > 1:
                                asset_text = text_parts[1].strip().split('\n')[0]
                                asset = asset_text.strip()
                    except:
                        pass
                    
                    # 如果没有找到资产信息，尝试其他方法
                    if not asset:
                        try:
                            # 在tweet文本中查找常见加密货币符号
                            container_text = container.text.upper()
                            crypto_symbols = ["BTC", "ETH", "SOL", "DOGE", "XRP", "BNB", "SHIB", 
                                            "ADA", "AVAX", "DOT", "MATIC", "LTC", "LINK", "PEPE"]
                            for symbol in crypto_symbols:
                                if f" {symbol} " in f" {container_text} ":
                                    asset = symbol
                                    break
                        except:
                            pass
                    
                    # 4. 提取时间
                    time_formatted = None
                    try:
                        # 尝试找到时间元素
                        time_elements = container.find_elements(By.CSS_SELECTOR, ".nav-time, .time, .timestamp")
                        for time_elem in time_elements:
                            time_text = time_elem.text.strip()
                            # 尝试提取时间格式
                            match = re.search(r'(\d{1,2}):(\d{2})(?::\d{2})?', time_text)
                            if match:
                                time_formatted = f"{match.group(1)}:{match.group(2)}"
                                break
                    except:
                        pass
                    
                    # 如果没有找到时间，使用当前时间
                    if not time_formatted:
                        time_formatted = datetime.now().strftime("%H:%M")
                    
                    # 只有在有资产信息和交易员名称的情况下才添加数据
                    if asset and trader_name:
                        current_items.append({
                            "asset": asset,
                            "sentiment": sentiment,
                            "time": time_formatted,
                            "trader": trader_name
                        })
                except Exception as e:
                    print(f"处理容器时出错: {str(e)}")
            
            # 统计新增的唯一评论数
            previous_unique_count = len(unique_comments)
            
            # 添加到唯一评论字典中
            for item in current_items:
                # 使用组合键确保唯一性
                key = f"{item['asset']}_{item['trader']}_{item['time']}_{item['sentiment']}"
                if key not in unique_comments:
                    unique_comments[key] = item
            
            # 计算新增的唯一评论数
            new_unique_count = len(unique_comments) - previous_unique_count
            print(f"本次滚动提取了 {len(current_items)} 条数据，其中 {new_unique_count} 条为新数据")
            print(f"当前已收集 {len(unique_comments)} 条唯一数据")
            
            # 检查是否有新的唯一评论
            if new_unique_count == 0:
                no_new_comments_count += 1
                print(f"连续 {no_new_comments_count} 次滚动没有新数据")
                
                # 如果连续多次没有新评论，尝试更激进的策略
                if no_new_comments_count == max_no_new_comments // 2:
                    print("尝试更激进的数据加载策略...")
                    
                    # 策略1: 尝试刷新页面
                    driver.refresh()
                    time.sleep(scroll_pause_time * 2)
                    
                    # 等待页面重新加载
                    WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.CLASS_NAME, "tweet-container"))
                    )
                    
                    # 快速滚动几次
                    for _ in range(5):
                        driver.execute_script(f"window.scrollBy(0, {random.randint(300, 800)});")
                        time.sleep(0.3)
                
                # 尝试强制触发vue-recycle-scroller更新
                if no_new_comments_count >= max_no_new_comments // 1.5:
                    print("尝试强制触发vue-recycle-scroller更新...")
                    
                    # 尝试执行一些可能触发vue-recycle-scroller更新的JavaScript代码
                    force_update_scripts = [
                        # 尝试重设窗口大小，可能触发重新计算
                        "window.dispatchEvent(new Event('resize'));",
                        # 尝试直接修改滚动位置
                        "window.scrollTo(0, Math.random() * document.body.scrollHeight);",
                        # 尝试模拟鼠标移动
                        "document.dispatchEvent(new MouseEvent('mousemove', {clientX: Math.random()*window.innerWidth, clientY: Math.random()*window.innerHeight, bubbles: true}));",
                        # 尝试触发滚动事件
                        "document.dispatchEvent(new Event('scroll', {bubbles: true}));"
                    ]
                    
                    for script in force_update_scripts:
                        try:
                            driver.execute_script(script)
                            time.sleep(0.5)
                        except:
                            pass
                
                # 如果达到最大无新评论次数，提前结束
                if no_new_comments_count >= max_no_new_comments:
                    print(f"连续 {max_no_new_comments} 次滚动没有发现新评论，提前结束滚动")
                    break
            else:
                no_new_comments_count = 0  # 重置计数器
        
        print("滚动完成，关闭浏览器...")
        
        # 关闭浏览器
        driver.quit()
        
        # 将字典转换回列表
        all_comments = list(unique_comments.values())
        print(f"总共收集到 {len(all_comments)} 条唯一评论")
        
        return all_comments
    
    except Exception as e:
        print(f"从URL抓取数据时出错: {str(e)}")
        # 打印详细的堆栈跟踪
        import traceback
        traceback.print_exc()
        return []

def try_click_load_more(driver, scroll_pause_time):
    """尝试查找并点击所有可能的"加载更多"按钮"""
    try:
        # 尝试多种可能的加载更多按钮的类名
        load_more_buttons = []
        for class_name in ["load-more", "load_more", "loadMore", "more-tweets", "more_tweets", "moreTweets", "show-more", "show_more", "showMore"]:
            buttons = driver.find_elements(By.CLASS_NAME, class_name)
            load_more_buttons.extend(buttons)
        
        # 也尝试通过文本内容找到按钮
        for text in ["加载更多", "Load More", "Show More", "更多", "More"]:
            xpath_buttons = driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')]")
            load_more_buttons.extend(xpath_buttons)
            
            xpath_links = driver.find_elements(By.XPATH, f"//a[contains(text(), '{text}')]")
            load_more_buttons.extend(xpath_links)
        
        # 点击找到的按钮
        for button in load_more_buttons:
            if button.is_displayed():
                print(f"找到并点击加载更多按钮: {button.get_attribute('class')}")
                # 尝试两种点击方式
                try:
                    driver.execute_script("arguments[0].click();", button)
                except:
                    try:
                        button.click()
                    except:
                        pass
                time.sleep(scroll_pause_time * 1.5)  # 点击后给更多时间加载
    except Exception as e:
        print(f"尝试点击加载更多按钮时出错: {str(e)}")
        return False
    return True

def extract_comments_from_soup(soup):
    """
    从BeautifulSoup对象中提取评论数据，专注于提取指定类名的元素
    """
    try:
        comments = []
        
        # 找到所有评论容器
        tweet_containers = soup.find_all('div', class_='tweet-container')
        print(f"找到 {len(tweet_containers)} 个tweet容器")
        
        success_count = 0
        failed_count = 0
        
        for container in tweet_containers:
            try:
                # 1. 直接提取交易员名称: class="name cursor-pointer"
                trader_name = None
                name_element = container.find('span', class_='name cursor-pointer')
                if name_element:
                    trader_name = name_element.text.strip()
                
                if not trader_name:
                    # 如果找不到特定类名，尝试退回到常规的类名
                    name_element = container.find('span', class_='name')
                    if name_element:
                        trader_name = name_element.text.strip()
                
                if not trader_name:
                    failed_count += 1
                    continue
                
                # 2. 提取情感态度: 直接找tweet-gpt-sentiment-xx类
                sentiment = "neutral"  # 默认值
                
                sentiment_positive = container.find('div', class_='tweet-gpt-sentiment-positive')
                sentiment_negative = container.find('div', class_='tweet-gpt-sentiment-negative')
                sentiment_neutral = container.find('div', class_='tweet-gpt-sentiment-neutral')
                
                if sentiment_positive:
                    sentiment = "positive"
                elif sentiment_negative:
                    sentiment = "negative"
                elif sentiment_neutral:
                    sentiment = "neutral"
                
                # 3. 提取时间
                time_formatted = None
                
                # 尝试从nav-right中找到时间
                nav_right = container.find('div', class_='nav-right')
                if nav_right:
                    time_element = nav_right.find('span', class_='nav-time')
                    if time_element:
                        time_text = time_element.text.strip()
                        match = re.search(r'(\d{1,2}):(\d{2})(?::\d{2})?', time_text)
                        if match:
                            time_formatted = f"{match.group(1)}:{match.group(2)}"
                
                # 如果没有找到时间，使用当前时间
                if not time_formatted:
                    time_formatted = datetime.now().strftime("%H:%M")
                
                # 4. 提取资产信息 - 直接查找"Asset Involved:"文本
                asset = ""
                container_text = container.text
                if "Asset Involved:" in container_text:
                    try:
                        # 切分文本并获取资产信息
                        parts = container_text.split("Asset Involved:")
                        if len(parts) > 1:
                            asset_info = parts[1].strip().split('\n')[0]
                            asset = asset_info.strip()
                    except:
                        pass
                
                # 如果没有找到资产，尝试从文本识别
                if not asset:
                    tweet_text_div = container.find('div', class_='tweet-text')
                    if tweet_text_div:
                        text = tweet_text_div.text.upper()
                        crypto_symbols = ["BTC", "ETH", "SOL", "DOGE", "XRP", "BNB", "SHIB", 
                                         "ADA", "AVAX", "DOT", "MATIC", "LTC", "LINK", "PEPE"]
                        for symbol in crypto_symbols:
                            if f" {symbol} " in f" {text} " or f" ${symbol} " in f" {text} ":
                                asset = symbol
                                break
                
                # 只在有资产和交易员的情况下添加
                if asset and trader_name:
                    comments.append({
                        "asset": asset,
                        "sentiment": sentiment,
                        "time": time_formatted,
                        "trader": trader_name
                    })
                    success_count += 1
                else:
                    failed_count += 1
                    
            except Exception as e:
                failed_count += 1
                print(f"解析单条评论时出错: {str(e)}")
                continue
        
        print(f"成功提取 {success_count} 条评论，跳过 {failed_count} 条")
        return comments
    except Exception as e:
        print(f"解析评论数据时出错: {str(e)}")
        return []

def extract_comments_from_html(html_file):
    """
    从保存的X-GPT HTML文件中提取评论数据
    返回包含资产、态度、时间、交易员信息的列表
    """
    try:
        with open(html_file, 'r', encoding='utf-8') as file:
            content = file.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        return extract_comments_from_soup(soup)
    except Exception as e:
        print(f"解析HTML文件时出错: {str(e)}")
        return []

def save_to_csv(comments, output_file):
    """保存评论数据到CSV文件"""
    try:
        # 如果没有评论，创建一个空文件并返回
        if not comments:
            with open(output_file, 'w', encoding='utf-8', newline='') as file:
                writer = csv.DictWriter(file, fieldnames=["asset", "sentiment", "time", "trader"])
                writer.writeheader()
            print(f"创建了空的CSV文件 {output_file}")
            return True
        
        # 按时间排序，最新的排在前面
        sorted_comments = sorted(comments, key=lambda x: x.get('time', '00:00'), reverse=True)
        
        with open(output_file, 'w', encoding='utf-8', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=["asset", "sentiment", "time", "trader"])
            writer.writeheader()
            for comment in sorted_comments:
                writer.writerow(comment)
        print(f"数据已保存到 {output_file}")
        return True
    except Exception as e:
        print(f"保存CSV文件时出错: {str(e)}")
        return False

def is_url(text):
    """判断文本是否为URL"""
    url_pattern = re.compile(
        r'^(?:http|https)://'  # http:// 或 https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'  # 域名
        r'localhost|'  # localhost
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP
        r'(?::\d+)?'  # 可选端口
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    return bool(url_pattern.match(text))

def main():
    # TODO: 维护God Trader list在本地，Quote，Tweet，Retweet等label，带上日期/
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="X-GPT 评论数据抓取工具")
    parser.add_argument("source", nargs="?", default="https://x-gpt.bwequation.com/",
                        help="HTML文件路径或URL (默认: https://x-gpt.bwequation.com/)")
    parser.add_argument("--scrolls", "-s", type=int, default=20,
                        help="滚动次数 (默认: 150)")
    parser.add_argument("--pause", "-p", type=float, default=2.5,
                        help="每次滚动后的等待时间 (秒) (默认: 2.5)")
    parser.add_argument("--output", "-o", type=str, default="",
                        help="输出CSV文件名 (默认: 当前日期.csv)")
    parser.add_argument("--max-no-new", "-m", type=int, default=15,
                        help="允许连续多少次滚动没有新评论后结束 (默认: 15)")
    parser.add_argument("--verbose", "-v", action="store_true",
                        help="显示详细输出")
    parser.add_argument("--human-like", "-hl", action="store_true",
                        help="使用更拟人化的浏览行为 (默认: 启用)")
    parser.add_argument("--report", "-r", action="store_true",
                        help="生成数据报告 (默认: 不生成)")
    parser.add_argument("--no-headless", "-nh", action="store_true",
                        help="非无头模式，显示浏览器窗口 (用于调试)")
    parser.add_argument("--browser", "-b", type=str, choices=["chrome", "edge"], default="edge",
                        help="选择使用的浏览器 (默认: edge)")
    
    args = parser.parse_args()
    
    # 设置开始时间
    start_time = datetime.now()
    
    # 设置输出文件名
    if args.output:
        output_file = args.output
    else:
        # 获取当前日期作为文件名
        today = datetime.now().strftime("%Y.%m.%d")
        output_file = f"{today}.csv"
    
    print(f"===== X-GPT 评论数据抓取工具 v3.0 =====")
    print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"参数: 滚动次数={args.scrolls}, 暂停时间={args.pause}秒, 最大无新评论次数={args.max_no_new}")
    print(f"使用浏览器: {args.browser.capitalize()}")
    if args.no_headless:
        print("注意: 使用非无头模式，将显示浏览器窗口")
    
    # 决定是从URL还是从文件中提取数据
    source = args.source
    if is_url(source):
        print(f"从URL抓取数据: {source}")
        comments = scrape_from_url(
            source, 
            args.scrolls, 
            args.pause, 
            max_no_new_comments=args.max_no_new, 
            headless=not args.no_headless,
            browser_type=args.browser
        )
    else:
        # 作为文件路径处理
        html_file = source
        if not os.path.exists(html_file):
            print(f"错误: 找不到HTML文件 '{html_file}'")
            return
        print(f"从HTML文件提取数据: {html_file}")
        comments = extract_comments_from_html(html_file)
        print(comments)
    
    # 计算运行时间
    end_time = datetime.now()
    run_time = end_time - start_time
    minutes, seconds = divmod(run_time.seconds, 60)
    
    # 保存评论并生成报告
    if save_to_csv(comments, output_file):
        print(f"\n===== 抓取完成 =====")
        print(f"运行时间: {minutes}分{seconds}秒")
        print(f"成功抓取并保存了 {len(comments)} 条唯一评论数据到 {output_file}")
        
        # 生成数据统计报告
        if args.report or args.verbose:
            generate_report(comments)
        
        # 打印前几条数据作为预览
        if args.verbose and comments:
            print("\n数据预览:")
            preview_count = min(5, len(comments))
            for i in range(preview_count):
                comment = comments[i]
                print(f"{i+1}. 资产: {comment['asset']}, 情绪: {comment['sentiment']}, 时间: {comment['time']}, 交易员: {comment['trader']}")
    else:
        print("\n===== 抓取完成 =====")
        print(f"运行时间: {minutes}分{seconds}秒")
        print("未能成功保存数据")

def generate_report(comments):
    """生成数据统计报告"""
    print("\n===== 数据分析报告 =====")
    
    # 统计资产分布
    assets = {}
    for comment in comments:
        asset = comment['asset']
        if asset in assets:
            assets[asset] += 1
        else:
            assets[asset] = 1
    
    # 按数量排序
    sorted_assets = sorted(assets.items(), key=lambda x: x[1], reverse=True)
    
    print(f"资产分布 (总计 {len(sorted_assets)} 种):")
    for asset, count in sorted_assets[:10]:  # 只显示前10种
        percent = count * 100 / len(comments)
        print(f"  {asset}: {count} 条 ({percent:.1f}%)")
    
    if len(sorted_assets) > 10:
        print(f"  其他: {sum([count for _, count in sorted_assets[10:]])} 条")
    
    # 统计情绪分布
    sentiments = {'positive': 0, 'negative': 0, 'neutral': 0}
    for comment in comments:
        sentiment = comment['sentiment'].lower()
        if sentiment in sentiments:
            sentiments[sentiment] += 1
        else:
            sentiments['neutral'] += 1  # 默认归类为中性
    
    print("\n情绪分布:")
    for sentiment, count in sentiments.items():
        if count > 0:
            percent = count * 100 / len(comments)
            print(f"  {sentiment}: {count} 条 ({percent:.1f}%)")
    
    # 统计交易员分布
    traders = {}
    for comment in comments:
        trader = comment['trader']
        if trader in traders:
            traders[trader] += 1
        else:
            traders[trader] = 1
    
    # 按数量排序
    sorted_traders = sorted(traders.items(), key=lambda x: x[1], reverse=True)
    
    print(f"\n交易员分布 (总计 {len(sorted_traders)} 位):")
    for trader, count in sorted_traders[:5]:  # 只显示前5位
        percent = count * 100 / len(comments)
        print(f"  {trader}: {count} 条 ({percent:.1f}%)")
    
    if len(sorted_traders) > 5:
        print(f"  其他交易员: {sum([count for _, count in sorted_traders[5:]])} 条")
    
    print("\n===== 报告结束 =====")

if __name__ == "__main__":
    main() 