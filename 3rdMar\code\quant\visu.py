import polars as pl
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.graph_objects as go

from abc import ABC, abstractmethod
import numpy as np


# layout (row, col, width, height)
DEFAULT_WIDTH = 800
DEFAULT_HEIGHT = 400
LAYOUT = {
    1: (1, 1, 400, 400),
    2: (1, 2, 800, 400),
    3: (1, 3, 1200, 400),
    4: (2, 3, 1200, 800),
    5: (2, 3, 1200, 800),
    6: (2, 3, 1200, 800),
    7: (3, 3, 1200, 1200),
    8: (3, 3, 1200, 1200)
}


class BaseCanvas(ABC):
    def __init__(self, row=1, col=1, subtitles=[], name="Canvas"):
        self.row = row
        self.col = col
        self.name = name
        self.width = 800
        self.height = 400
        self.total = 16 # self.col * self.row
        self.layout = LAYOUT
        self.current = 0
        subtitles.extend(["default"] * max(0, self.total - len(subtitles)))
        self.subtitles = subtitles
        self._objs = []
        self._objs_x_axis = ["x"] * self.total
        self._objs_y_axis = ["y"] * self.total

    def reset(self, row=1, col=1, subtitles=[], name=None, width=DEFAULT_WIDTH, height=DEFAULT_HEIGHT):
        self.row = row
        self.col = col
        self.name = name if name else self.name
        self.width = width
        self.height = height
        self.total = 16 # self.col * self.row
        self.layout = LAYOUT
        self.current = 0
        subtitles.extend(["default"] * max(0, self.total - len(subtitles)))
        self.subtitles = subtitles
        self._objs = []
        self._objs_x_axis = ["x"] * self.total
        self._objs_y_axis = ["y"] * self.total


    def _auto_layout(self):
        l = len(self._objs)
        if l < 8 :
            self.row, self.col, width, height = self.layout[l]
        else:
            r = l // 3 + 1
            self.row, self.col, width, height = (r, 3, 1200, 400 * r)
        if DEFAULT_WIDTH == self.width:
            self.width, self.height = (width, height)

    def _auto_rc(self, idx=None):
        if idx is not None:
            r = int(idx / self.col + 1)
            c = int(idx % self.col + 1)
        else:
            r = int(self.current / self.col + 1)
            c = int(self.current % self.col + 1)
        return r, c
    
    def _update(self, trace, x_axis, y_axis, idx:int=None):
        if isinstance(idx, int) and idx >= 0 and idx <= len(self._objs): # for multiple obj in a subplot
            try: self._objs[idx]
            except: self._objs.append([])
            if isinstance(self._objs[idx], go.Scatter):
                tmp = self._objs[idx]
                self._objs[idx] = [{
                    "obj": tmp,
                    # "x_axis": self._objs_x_axis[idx], # TODO: currently useless
                    # "y_axis": self._objs_y_axis[idx]
                }]
            self._objs[idx].append({
                "obj": trace,
                # "x_axis": x_axis,
                # "y_axis": y_axis
            })
        else:
            self._objs.append(trace)
            self._objs_x_axis[self.current] = x_axis
            self._objs_y_axis[self.current] = y_axis
            self.current += 1


    def show(self):
        if self.current > self.total:
            raise ValueError("Number of objects exceeds the total number of subplots")
        if self.current <= 0:
            raise ValueError("No objects to show")

        self._auto_layout()
        self._fig = make_subplots(rows=self.row, cols=self.col, subplot_titles=self.subtitles)
        for idx, obj in enumerate(self._objs):
            r, c = self._auto_rc(idx)
            if isinstance(obj, list):
                for sub_obj in obj:
                    self._fig.add_trace(sub_obj["obj"], row=r, col=c)
            else:
                self._fig.add_trace(obj, row=r, col=c)
                self._fig.update_xaxes(title_text=self._objs_x_axis[idx], row=r, col=c)
                self._fig.update_yaxes(title_text=self._objs_y_axis[idx], row=r, col=c)

        self._fig.update_layout(
            title_text=self.name,
            width=self.width,
            height=self.height
        ).show()


    def line(self, df, x, y, name=None):
        trace = go.Scatter(
            x=df[x],
            y=df[y],
            mode='lines',
            name=name if name else 'Line'
        )
        if name is not None:
            self.subtitles[self.current] = name
        self._update(trace, x, y)

    def nline(self, df, x, ys:list, names:list=None):
        if names:
            if not isinstance(names, list) or len(names) != len(ys):
                raise ValueError("names should be a list and len(names) must equals to len(ys)")
        for idx, y in enumerate(ys):
            trace = go.Scatter(
                x=df[x],
                y=df[y],
                mode='lines',
                name=names[idx] if names else 'Line'
            )
            self._update(trace, x, y, self.current)
        self.current += 1 # TODO: now you need to update manually

    def scatter(self, df, x, y, name=None):
        trace = go.Scatter(
            x=df[x],
            y=df[y],
            mode='markers',
            name=name if name else 'Scatter'
        )
        if name is not None:
            self.subtitles[self.current] = name
        self._update(trace, x, y)

    def bar(self, df, x, y, name=None):
        trace = go.Bar(
            x=df[x],
            y=df[y],
            name=name if name else 'Bar'
        )
        if name is not None:
            self.subtitles[self.current] = name
        self._update(trace, x, y)

    def histogram(self, df, x, name=None):
        trace = go.Histogram(
            x=df[x],
            name=name if name else 'Histogram'
        )
        if name is not None:
            self.subtitles[self.current] = name
        self._update(trace, x, "Count")

    def bubble(self, keys:list, values:list, sizer:int=4, name=None):
        # normalize
        max_value = max(values)
        normalized_values = [v/max_value for v in values]
        # 使用极坐标，角度均匀分布，半径由值决定(值越大半径越小)
        angles = np.linspace(0, 2*np.pi, len(values), endpoint=False)
        radii = [1 - val*0.8 for val in normalized_values]
        # 转换为直角坐标
        x = [r * np.cos(theta) for r, theta in zip(radii, angles)]
        y = [r * np.sin(theta) for r, theta in zip(radii, angles)]

        trace = go.Scatter(
            x=x,
            y=y,
            mode='markers+text',
            marker=dict(
                size=[v*sizer for v in values],  # 点的大小与值成正比
                color=values,  # 颜色也与值相关
                colorscale='Viridis',
                showscale=True,
                sizemode='diameter'
            ),
            text=keys,
            textposition='middle center',
            hoverinfo='text',  # 只显示文本
            hovertext=[f"{k}: {v}" for k, v in zip(keys, values)],
            name=name if name else 'Bubble'
        )
        if name is not None:
            self.subtitles[self.current] = name
        self._update(trace, "x", "y")
