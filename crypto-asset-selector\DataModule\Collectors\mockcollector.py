import os
from typing import Dict, Any

from .base_collector import BaseCollector

class MockCollector(BaseCollector):
    """模拟数据收集器，用于测试扩展性"""
    
    def __init__(self, 
                 workers: int = 12, 
                 max_retries: int = 3, 
                 api_limit: int = 60,
                 source_name: str = "MockSource"):
        """
        初始化模拟收集器
        
        参数:
        workers (int): 线程池工作线程数
        max_retries (int): 最大重试次数
        api_limit (int): API每分钟请求数限制
        source_name (str): 数据源名称
        """
        super().__init__(workers, max_retries, api_limit)
        self.source_name = source_name
    
    def pre_handle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """预处理参数"""
        params["source_name"] = params.get("source_name", self.source_name)
        return params
    
    def handle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据获取"""
        source_name = params.get("source_name")
        
        # 如果有依赖结果，可以使用
        dependency_data = params.get("dependency_data")
        
        if dependency_data:
            print(f"Mock收集器收到了依赖数据: {dependency_data[:100]}...")
        
        return {
            "success": True,
            "data": f"这是来自 {source_name} 的模拟数据",
            "message": f"成功从 {source_name} 获取模拟数据"
        }
    
    def post_handle(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """后处理结果"""
        # 在实际结果上附加一些元数据
        result["metadata"] = {
            "collector_type": "MockCollector",
            "timestamp": "2023-05-01T12:00:00Z"
        }
        return result 