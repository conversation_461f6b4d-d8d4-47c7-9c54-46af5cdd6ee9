# TODO

# TODO
import polars as pl
from tornado.httputil import url_concat
from core.http import async_get_request
import json
import os
import asyncio
from common import utils


TOKEN_INSIGHT_GET_COIN_HISTORY = "https://api.tokeninsight.com/api/v1/history/coins/"
TOKEN_INSIGHT_GET_COIN_LIST = "https://api.tokeninsight.com/api/v1/coins/list"

TOKEN_INSIGHT_API_LIMIT = 60

TOKEN_INSIGHT_API_KEY = "35b31ee372f54785a422561b2356d957"



def _get_ti_headers():

    return {
        'TI_API_KEY': TOKEN_INSIGHT_API_KEY,
        'accept': 'application/json'
    }

#TODO: move to quant, unify the logs
def filter_ti_coin_list_by_symbol_list(df:pl.DataFrame, symbol_list_path:str = "symbol_list.csv") -> pl.DataFrame:
    """
    filter coin list by symbol_list.csv

    params:
    df (polars.DataFrame): raw coin list
    symbol_list_path (str): path of symbol_list.csv

    returns:
    polars.DataFrame: filtered coin list
    """
    try:
        if not os.path.exists(symbol_list_path):
            print(f"symbol list not found: {symbol_list_path}")
            return df

        symbol_df = pl.read_csv(symbol_list_path)
        # filter coin list by symbol_list.csv, keep the one with largest spot_volume_24h if there are multiple coins with the same symbol
        filtered_df = df.filter(pl.col("symbol").is_in(symbol_df["symbol"])).sort("spot_volume_24h", descending=True).unique(subset=["symbol"], keep="first")


        print(f"filtered coin list from {len(df)} to {len(filtered_df)}")
        return filtered_df

    except Exception as e:
        print(f"filter_ti_coin_list_by_symbol_list Error: {e}")
        return df

async def _fetch_coin_list(limit:int = 1500, offset:int = 0, vs_currency:str = "usd"):
    """
    fetch coin list from tokeninsight

    params:
    limit (int): number of coins to return, max 1500
    offset (int): offset of the list
    vs_currency (str): vs currency, default usd

    returns:
    polars.DataFrame: coin list
    """
    url = TOKEN_INSIGHT_GET_COIN_LIST
    params = {
        "limit": limit,
        "offset": offset,
        "vs_currency": vs_currency
    }
    url = url_concat(url, params)
    headers = _get_ti_headers()
    headers.update({
        'User-Agent': 'python-requests/2.28.1',
        'Accept-Encoding': 'gzip, deflate',
        'Accept': '*/*',
        'Connection': 'keep-alive'
    })



    response = await async_get_request(url, headers=headers)
    if response.code == 200:
        data = json.loads(response.body)
        if data["status"]["code"] == 0:
            coins_data = data["data"]["items"]

            # extract the needed columns TODO:换成原生API转换
            df = pl.DataFrame([{
                "price": coin.get("price"),
                "symbol": coin.get("symbol"),
                "id": coin.get("id"),
                "spot_volume_24h": coin.get("spot_volume_24h"),
                "price_change_percentage_24h": coin.get("price_change_percentage_24h")
            } for coin in coins_data])

            return df
        else:
            raise Exception(f"ti._fetch_coin_list Error: {data['status']['message']}")
    else:
        raise Exception(f"ti._fetch_coin_list Error: {response.body}")


async def _fetch_coin_mkc_history(coin_id:str, interval:str = "day", length:str = -1, vs_currency:str = "usd"):
    """
    fetch coin mkc history from tokeninsight

    params:
    coin_id (str): coin id in token insight
    interval (str): time interval, default day
    length (int): length of the history, max -1, default -1
    vs_currency (str): vs currency, default usd

    returns: df: raw data of one coin
        #    symbol: symbol of the coin, used for path name and interval file
        #    first_timestamp: first timestamp of the history, used for interval file
        #    last_timestamp: last timestamp of the history, used for interval file
        #    current_market_cap: current market cap of the coin, used for interval file

    """
    url = f"{TOKEN_INSIGHT_GET_COIN_HISTORY}{coin_id}"
    params = {
        "interval": interval,
        "length": length,
        "vs_currency": vs_currency
    }

    headers = _get_ti_headers()

    headers.update({
        'User-Agent': 'python-requests/2.28.1',
        'Accept-Encoding': 'gzip, deflate',
        'Accept': '*/*',
        'Connection': 'keep-alive'
    })



        
    response = await async_get_request(url, headers=headers)
    
    if response.code == 200:
        data = json.loads(response.body) 
        if data["status"]["code"] == 0:
            history_data = data["data"]["market_chart"]
            symbol = data["data"]["symbol"]

            if not history_data:
                print("No history data for {coin_id}")
                return None, symbol, None, None, None

            # extract the needed columns TODO:换成原生API转换
            df = pl.DataFrame([{
                "price": item.get("price"),
                "vol_spot_24h": item.get("vol_spot_24h"),
                "market_cap": item.get("market_cap"),
                "timestamp": item.get("timestamp")
            } for item in history_data])

            # TODO: move extract interval to quant
            # extract the first and last timestamp for interval file
            timestamps = sorted([item.get("timestamp") for item in history_data])
            first_timestamp = timestamps[0] if timestamps else None
            last_timestamp = timestamps[-1] if timestamps else None

            # extract the current market cap for interval file
            latest_data = sorted(history_data, key=lambda x: x.get("timestamp", 0), reverse=True)[0]
            current_market_cap = latest_data.get("market_cap")

            return df, symbol, first_timestamp, last_timestamp, current_market_cap
        else:
            raise Exception(f"ti._fetch_coin_mkc_history Error: {data['status']['message']}")
    else:
        raise Exception(f"ti._fetch_coin_mkc_history Error: {response.body}")    

    

COIN_POOL = 15704
FETCH_LIMIT = 1500
COIN_LIST_CACHE = "collected_coins.parquet"

async def _get_coin_list():

    if os.path.exists(COIN_LIST_CACHE):
        df = pl.read_parquet(COIN_LIST_CACHE)
        return filter_ti_coin_list_by_symbol_list(df)
    
    all_coins_df = None
    offset = 0
    total_batches = (COIN_POOL + FETCH_LIMIT - 1) // FETCH_LIMIT
    batches_count = 0

    while offset < COIN_POOL:
        batches_count += 1
        
        #TODO: use logger
        print(f"Fetching coin list, batch: {batches_count}/{total_batches}, offset: {offset}")

        #TODO: check API rate limit
        current_limit = min(FETCH_LIMIT, COIN_POOL - offset)

        try:
            batch_df = await _fetch_coin_list(limit=current_limit, offset=offset)
        except Exception as e:
            print(f"Error fetching coin list, offset: {offset}, error: {e}")
            #TODO: retry and switch APIKey
            offset += current_limit
            continue

        if all_coins_df is None:
            all_coins_df = batch_df
        else:
            all_coins_df = pl.concat([all_coins_df, batch_df])
        
        offset += current_limit

        if len(batch_df) < current_limit:
            print(f"Reached the end of coin list, total: {len(all_coins_df)}")
            break


    all_coins_df.write_parquet(COIN_LIST_CACHE)
    filtered_df = filter_ti_coin_list_by_symbol_list(all_coins_df)

    return filtered_df

retry_queue = asyncio.Queue()
outbound = asyncio.Queue()

async def _collect_coin_mkc_full_history():
    
    coin_list_df = await _get_coin_list()

    if coin_list_df is None or coin_list_df.is_empty():
        print("No coin list found")
        return
    
    print(f"{len(coin_list_df)} coins fetched")

    coins_data = coin_list_df.select(["id", "symbol"]).to_dicts()

    # TODO: df保存方案选型，逐一save还是批量save
    for id, symbol in coins_data:
        try:
            #TODO: check API rate limit
            df, symbol, first_timestamp, last_timestamp, current_market_cap = await _fetch_coin_mkc_history(id)
            if df is not None and not df.is_empty():
                #TODO: save to parquet
                filename = f"{symbol}_{id}_history_{first_timestamp}_{last_timestamp}.parquet"
                df.write_parquet(filename)
                print(f"History data saved for {symbol}({id})")
            else:
                print(f"No history data for {symbol}({id})")
        except Exception as e:
            print(f"Error fetching coin mkc history for {symbol}({id}): {e}")
            #TODO: retry and switch APIKey
            await retry_queue.put(_fetch_coin_mkc_history(id))


async def _collect_coin_mkc_incremental_history():
    pass

async def main():
    pass